import {
	CampaignStatus,
	InternalDiscordNotificationTypes,
	LinkedinSendMessageTurnMessageTypes,
	ModelNames,
	OpenAIAzureVersions,
	PersonaTones,
	UserSentiment,
} from "./enums";

export const LANGUAGES = [
	"Abkhaz",
	"Afar",
	"Afrikaans",
	"Akan",
	"Albanian",
	"Amharic",
	"Arabic",
	"Aragonese",
	"Armenian",
	"Assamese",
	"Avaric",
	"Avestan",
	"Aymara",
	"Azerbaijani",
	"Bambara",
	"Bashkir",
	"Basque",
	"Belarusian",
	"Bengali",
	"Bihari",
	"Bislama",
	"Bosnian",
	"Breton",
	"Bulgarian",
	"Burmese",
	"Catalan; Valencian",
	"Chamorro",
	"Chechen",
	"Chichewa; Chewa; Nyanja",
	"Chinese",
	"Chuvash",
	"Cornish",
	"Corsican",
	"Cree",
	"Croatian",
	"Czech",
	"Danish",
	"Divehi; Dhivehi; Maldivian;",
	"Dutch",
	"English",
	"Esperanto",
	"Estonian",
	"Ewe",
	"Faroese",
	"Fijian",
	"Finnish",
	"French",
	"Fula; Fulah; <PERSON><PERSON>ar; Pular",
	"Galician",
	"Georgian",
	"German",
	"Greek, Modern",
	"Guaraní",
	"Gujarati",
	"Haitian; Haitian Creole",
	"Hausa",
	"Hebrew (modern)",
	"Herero",
	"Hindi",
	"Hiri Motu",
	"Hungarian",
	"Interlingua",
	"Indonesian",
	"Interlingue",
	"Irish",
	"Igbo",
	"Inupiaq",
	"Ido",
	"Icelandic",
	"Italian",
	"Inuktitut",
	"Japanese",
	"Javanese",
	"Kalaallisut, Greenlandic",
	"Kannada",
	"Kanuri",
	"Kashmiri",
	"Kazakh",
	"Khmer",
	"Kikuyu, Gikuyu",
	"Kinyarwanda",
	"Kirghiz, Kyrgyz",
	"Komi",
	"Kongo",
	"Korean",
	"Kurdish",
	"Kwanyama, Kuanyama",
	"Latin",
	"Luxembourgish, Letzeburgesch",
	"Luganda",
	"Limburgish, Limburgan, Limburger",
	"Lingala",
	"Lao",
	"Lithuanian",
	"Luba-Katanga",
	"Latvian",
	"Manx",
	"Macedonian",
	"Malagasy",
	"Malay",
	"Malayalam",
	"Maltese",
	"Māori",
	"Marathi (Marāṭhī)",
	"Marshallese",
	"Mongolian",
	"Nauru",
	"Navajo, Navaho",
	"Norwegian Bokmål",
	"North Ndebele",
	"Nepali",
	"Ndonga",
	"Norwegian Nynorsk",
	"Norwegian",
	"Nuosu",
	"South Ndebele",
	"Occitan",
	"Ojibwe, Ojibwa",
	"Old Church Slavonic, Church Slavic, Church Slavonic, Old Bulgarian, Old Slavonic",
	"Oromo",
	"Oriya",
	"Ossetian, Ossetic",
	"Panjabi, Punjabi",
	"Pāli",
	"Persian",
	"Polish",
	"Pashto, Pushto",
	"Portuguese",
	"Quechua",
	"Romansh",
	"Kirundi",
	"Romanian, Moldavian, Moldovan",
	"Russian",
	"Sanskrit (Saṁskṛta)",
	"Sardinian",
	"Sindhi",
	"Northern Sami",
	"Samoan",
	"Sango",
	"Serbian",
	"Scottish Gaelic; Gaelic",
	"Shona",
	"Sinhala, Sinhalese",
	"Slovak",
	"Slovene",
	"Somali",
	"Southern Sotho",
	"Spanish; Castilian",
	"Sundanese",
	"Swahili",
	"Swati",
	"Swedish",
	"Tamil",
	"Telugu",
	"Tajik",
	"Thai",
	"Tigrinya",
	"Tibetan Standard, Tibetan, Central",
	"Turkmen",
	"Tagalog",
	"Tswana",
	"Tonga (Tonga Islands)",
	"Turkish",
	"Tsonga",
	"Tatar",
	"Twi",
	"Tahitian",
	"Uighur, Uyghur",
	"Ukrainian",
	"Urdu",
	"Uzbek",
	"Venda",
	"Vietnamese",
	"Volapük",
	"Walloon",
	"Welsh",
	"Wolof",
	"Western Frisian",
	"Xhosa",
	"Yiddish",
	"Yoruba",
	"Zhuang, Chuang",
] as const;

export const INTERNAL_DISCORD_NOTIFICATION_TYPES = {
	[InternalDiscordNotificationTypes.GENERAL]: {
		name: "General notifications",
		webhookURL:
			"https://discord.com/api/webhooks/1218877707824267325/bhEZtBKGgTdctF9wnC_7-NViec9dZz8GUfviQeqfj1_VZzExkxMLFQ5WIg5oSvuKHml2",
	},
	[InternalDiscordNotificationTypes.CAMPAIGN]: {
		name: "Campaign notifications",
		webhookURL:
			"https://discord.com/api/webhooks/1218866088268271626/hP-1LepZdQ-KfCZZ8wxHI-KUsC_XRKcwkJ9b6G9QDOxxvJIn-IdCNPloV-Ngkpsm-VJq",
	},
	[InternalDiscordNotificationTypes.SECOND_BRAIN]: {
		name: "Second brain notifications",
		webhookURL:
			"https://discord.com/api/webhooks/1218823042629898362/rU0e4LdXNiw98EBmgcog3W0ElKcEZjkUG3edeDI5mm889Y2_lzq96GZ4l6N5C4tQWOoH",
	},
	[InternalDiscordNotificationTypes.USER_BULK_ADDER]: {
		name: "User bulk adder notifications",
		webhookURL:
			"https://discord.com/api/webhooks/1218861501998436454/SLZp4v8mbgG75tYKfPLue92FCc2mkggofMfTtTsl7WLlLeBO-3aH0NLph7nNNiBBFFQk",
	},
	[InternalDiscordNotificationTypes.HALLUCINATION]: {
		name: "Hallucination notifications",
		webhookURL:
			"https://discord.com/api/webhooks/1233351944211267696/x_Bz3fewYgDruAcM9u0jqOAmuk7W0P-ye8pBBKlnXL3XCKPmlPZJyfQcEWGuAxdXEEPY",
	},
} as const;

export const SALES_USER_SENTIMENTS = [
	{
		id: UserSentiment.SKEPTICISM,
		sentiment: "skepticism",
	},
	{
		id: UserSentiment.FRUSTRATION,
		sentiment: "frustration",
	},
	{
		id: UserSentiment.ANGER,
		sentiment: "anger",
	},
	{
		id: UserSentiment.NEUTRALITY,
		sentiment: "neutrality",
	},
	{
		id: UserSentiment.CURIOSITY,
		sentiment: "curiosity",
	},
	{
		id: UserSentiment.EXCITEMENT,
		sentiment: "excitement",
	},
];

export const SURVEY_USER_SENTIMENTS = [
	{
		id: UserSentiment.FRUSTRATION,
		sentiment: "frustration",
	},
	{
		id: UserSentiment.DISAPPOINTMENT,
		sentiment: "disappointment",
	},
	{
		id: UserSentiment.NEUTRALITY,
		sentiment: "neutrality",
	},
	{
		id: UserSentiment.CURIOSITY,
		sentiment: "curiosity",
	},
	{
		id: UserSentiment.FULFILLMENT,
		sentiment: "fulfillment",
	},
];

export const SUPPORT_USER_SENTIMENTS = SURVEY_USER_SENTIMENTS;

export const INDUSTRIES = [
	{
		id: 1,
		value: "Accounting",
	},
	{
		id: 2,
		value: "Airlines/Aviation",
	},
	{
		id: 3,
		value: "Alternative Dispute Resolution",
	},
	{
		id: 4,
		value: "Alternative Medicine",
	},
	{
		id: 5,
		value: "Animation",
	},
	{
		id: 6,
		value: "Apparel & Fashion",
	},
	{
		id: 7,
		value: "Architecture & Planning",
	},
	{
		id: 8,
		value: "Arts & Crafts",
	},
	{
		id: 9,
		value: "Automotive",
	},
	{
		id: 10,
		value: "Aviation & Aerospace",
	},
	{
		id: 11,
		value: "Banking",
	},
	{
		id: 12,
		value: "Biotechnology",
	},
	{
		id: 13,
		value: "Broadcast Media",
	},
	{
		id: 14,
		value: "Building Materials",
	},
	{
		id: 15,
		value: "Business Supplies & Equipment",
	},
	{
		id: 16,
		value: "Capital Markets",
	},
	{
		id: 17,
		value: "Chemicals",
	},
	{
		id: 18,
		value: "Civic & Social Organization",
	},
	{
		id: 19,
		value: "Civil Engineering",
	},
	{
		id: 20,
		value: "Commercial Real Estate",
	},
	{
		id: 21,
		value: "Computer & Network Security",
	},
	{
		id: 22,
		value: "Computer Games",
	},
	{
		id: 23,
		value: "Computer Hardware",
	},
	{
		id: 24,
		value: "Computer Networking",
	},
	{
		id: 25,
		value: "Computer Software",
	},
	{
		id: 26,
		value: "Construction",
	},
	{
		id: 27,
		value: "Consumer Electronics",
	},
	{
		id: 28,
		value: "Consumer Goods",
	},
	{
		id: 29,
		value: "Consumer Services",
	},
	{
		id: 30,
		value: "Cosmetics",
	},
	{
		id: 31,
		value: "Dairy",
	},
	{
		id: 32,
		value: "Defense & Space",
	},
	{
		id: 33,
		value: "Design",
	},
	{
		id: 34,
		value: "Education Management",
	},
	{
		id: 35,
		value: "E-learning",
	},
	{
		id: 36,
		value: "Electrical & Electronic Manufacturing",
	},
	{
		id: 37,
		value: "Entertainment",
	},
	{
		id: 38,
		value: "Environmental Services",
	},
	{
		id: 39,
		value: "Events Services",
	},
	{
		id: 40,
		value: "Executive Office",
	},
	{
		id: 41,
		value: "Facilities Services",
	},
	{
		id: 42,
		value: "Farming",
	},
	{
		id: 43,
		value: "Financial Services",
	},
	{
		id: 44,
		value: "Fine Art",
	},
	{
		id: 45,
		value: "Fishery",
	},
	{
		id: 46,
		value: "Food & Beverages",
	},
	{
		id: 47,
		value: "Food Production",
	},
	{
		id: 48,
		value: "Fundraising",
	},
	{
		id: 49,
		value: "Furniture",
	},
	{
		id: 50,
		value: "Gambling & Casinos",
	},
	{
		id: 51,
		value: "Glass, Ceramics & Concrete",
	},
	{
		id: 52,
		value: "Government Administration",
	},
	{
		id: 53,
		value: "Government Relations",
	},
	{
		id: 54,
		value: "Graphic Design",
	},
	{
		id: 55,
		value: "Health, Wellness & Fitness",
	},
	{
		id: 56,
		value: "Higher Education",
	},
	{
		id: 57,
		value: "Hospital & Health Care",
	},
	{
		id: 58,
		value: "Hospitality",
	},
	{
		id: 59,
		value: "Human Resources",
	},
	{
		id: 60,
		value: "Import & Export",
	},
	{
		id: 61,
		value: "Individual & Family Services",
	},
	{
		id: 62,
		value: "Industrial Automation",
	},
	{
		id: 63,
		value: "Information Services",
	},
	{
		id: 64,
		value: "Information Technology & Services",
	},
	{
		id: 65,
		value: "Insurance",
	},
	{
		id: 66,
		value: "International Affairs",
	},
	{
		id: 67,
		value: "International Trade & Development",
	},
	{
		id: 68,
		value: "Internet",
	},
	{
		id: 69,
		value: "Investment Banking/Venture",
	},
	{
		id: 70,
		value: "Investment Management",
	},
	{
		id: 71,
		value: "Judiciary",
	},
	{
		id: 72,
		value: "Law Enforcement",
	},
	{
		id: 73,
		value: "Law Practice",
	},
	{
		id: 74,
		value: "Legal Services",
	},
	{
		id: 75,
		value: "Legislative Office",
	},
	{
		id: 76,
		value: "Leisure & Travel",
	},
	{
		id: 77,
		value: "Libraries",
	},
	{
		id: 78,
		value: "Logistics & Supply Chain",
	},
	{
		id: 79,
		value: "Luxury Goods & Jewelry",
	},
	{
		id: 80,
		value: "Machinery",
	},
	{
		id: 81,
		value: "Management Consulting",
	},
	{
		id: 82,
		value: "Maritime",
	},
	{
		id: 83,
		value: "Marketing & Advertising",
	},
	{
		id: 84,
		value: "Market Research",
	},
	{
		id: 85,
		value: "Mechanical or Industrial Engineering",
	},
	{
		id: 86,
		value: "Media Production",
	},
	{
		id: 87,
		value: "Medical Device",
	},
	{
		id: 88,
		value: "Medical Practice",
	},
	{
		id: 89,
		value: "Mental Health Care",
	},
	{
		id: 90,
		value: "Military",
	},
	{
		id: 91,
		value: "Mining & Metals",
	},
	{
		id: 92,
		value: "Motion Pictures & Film",
	},
	{
		id: 93,
		value: "Museums & Institutions",
	},
	{
		id: 94,
		value: "Music",
	},
	{
		id: 95,
		value: "Nanotechnology",
	},
	{
		id: 96,
		value: "Newspapers",
	},
	{
		id: 97,
		value: "Nonprofit Organization Management",
	},
	{
		id: 98,
		value: "Oil & Energy",
	},
	{
		id: 99,
		value: "Online Publishing",
	},
	{
		id: 100,
		value: "Outsourcing/Offshoring",
	},
	{
		id: 101,
		value: "Package/Freight Delivery",
	},
	{
		id: 102,
		value: "Packaging & Containers",
	},
	{
		id: 103,
		value: "Paper & Forest Products",
	},
	{
		id: 104,
		value: "Performing Arts",
	},
	{
		id: 105,
		value: "Pharmaceuticals",
	},
	{
		id: 106,
		value: "Philanthropy",
	},
	{
		id: 107,
		value: "Photography",
	},
	{
		id: 108,
		value: "Plastics",
	},
	{
		id: 109,
		value: "Political Organization",
	},
	{
		id: 110,
		value: "Primary/Secondary",
	},
	{
		id: 111,
		value: "Printing",
	},
	{
		id: 112,
		value: "Professional Training",
	},
	{
		id: 113,
		value: "Program Development",
	},
	{
		id: 114,
		value: "Public Policy",
	},
	{
		id: 115,
		value: "Public Relations",
	},
	{
		id: 116,
		value: "Public Safety",
	},
	{
		id: 117,
		value: "Publishing",
	},
	{
		id: 118,
		value: "Railroad Manufacture",
	},
	{
		id: 119,
		value: "Ranching",
	},
	{
		id: 120,
		value: "Real Estate",
	},
	{
		id: 121,
		value: "Recreational",
	},
	{
		id: 122,
		value: "Facilities & Services",
	},
	{
		id: 123,
		value: "Religious Institutions",
	},
	{
		id: 124,
		value: "Renewables & Environment",
	},
	{
		id: 125,
		value: "Research",
	},
	{
		id: 126,
		value: "Restaurants",
	},
	{
		id: 127,
		value: "Retail",
	},
	{
		id: 128,
		value: "Security & Investigations",
	},
	{
		id: 129,
		value: "Semiconductors",
	},
	{
		id: 130,
		value: "Shipbuilding",
	},
	{
		id: 131,
		value: "Sporting Goods",
	},
	{
		id: 132,
		value: "Sports",
	},
	{
		id: 133,
		value: "Staffing & Recruiting",
	},
	{
		id: 134,
		value: "Supermarkets",
	},
	{
		id: 135,
		value: "Telecommunications",
	},
	{
		id: 136,
		value: "Textiles",
	},
	{
		id: 137,
		value: "Think Tanks",
	},
	{
		id: 138,
		value: "Tobacco",
	},
	{
		id: 139,
		value: "Translation & Localization",
	},
	{
		id: 140,
		value: "Transportation/Trucking/Railroad",
	},
	{
		id: 141,
		value: "Utilities",
	},
	{
		id: 142,
		value: "Venture Capital",
	},
	{
		id: 143,
		value: "Veterinary",
	},
	{
		id: 144,
		value: "Warehousing",
	},
	{
		id: 145,
		value: "Wholesale",
	},
	{
		id: 146,
		value: "Wine & Spirits",
	},
	{
		id: 147,
		value: "Wireless",
	},
	{
		id: 148,
		value: "Writing & Editing",
	},
];

export const INACTIVE_CAMPAIGN_STATUSES = [
	CampaignStatus.PENDING_APPROVAL,
	CampaignStatus.APPROVED,
	CampaignStatus.SCHEDULED,
	CampaignStatus.ENDED,
	CampaignStatus.CREATED,
];

export const ACTIVE_CAMPAIGN_STATUSES = [
	CampaignStatus.SENDING_INITIAL_MESSAGES,
	CampaignStatus.RESENDING_INITIAL_MESSAGES,
	CampaignStatus.SENDING_INITIAL_MESSAGE_FOLLOWUPS,
	CampaignStatus.RUNNING,
];

export const OPENAI_RESOURCES = [
	{
		name: "sbl-openai-east-us",
		region: "east-us",
		apiKey: "b4e4948f46cf43dea3e7f0d8e0e37e58",
		deployments: [
			{
				name: ModelNames.TEXT_EMBEDDING_ADA_002,
				model: ModelNames.TEXT_EMBEDDING_ADA_002,
				apiVersion: OpenAIAzureVersions["2023-05-15"],
			},
			{
				name: ModelNames.GPT_4O_MINI,
				model: ModelNames.GPT_4O_MINI,
				apiVersion: OpenAIAzureVersions["2023_03_15_PREVIEW"],
			},
			{
				name: ModelNames.GPT_4_TURBO_2024_04_09,
				model: ModelNames.GPT_4_TURBO_2024_04_09,
				apiVersion: OpenAIAzureVersions["2023_03_15_PREVIEW"],
			},
		],
	},
];

export const PERSONA_TONES = [
	{
		id: PersonaTones.PROFESSIONAL,
		tone: "professional",
	},
	{
		id: PersonaTones.FRIENDLY,
		tone: "friendly",
	},
	{
		id: PersonaTones.PERSUASIVE,
		tone: "persuasive",
	},
	{
		id: PersonaTones.EMPATHETIC,
		tone: "empathetic",
	},
	{
		id: PersonaTones.CURIOUS,
		tone: "curious",
	},
	{
		id: PersonaTones.HUMOROUS,
		tone: "humorous",
	},
];

export const LINKEDIN_SEND_MESSAGE_TURN_MESSAGE_TYPES = [
	LinkedinSendMessageTurnMessageTypes.INITIAL_MESSAGE,
	LinkedinSendMessageTurnMessageTypes.FOLLOW_UP_MESSAGE,
	LinkedinSendMessageTurnMessageTypes.NON_INITIAL_OR_FOLLOW_UP_MESSAGE,
];
