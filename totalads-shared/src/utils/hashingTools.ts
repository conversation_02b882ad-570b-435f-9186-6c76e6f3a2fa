import bcrypt from "@node-rs/bcrypt";
const saltRounds = +(process.env.HASHING_SALT_ROUNDS as string);

const hashPassword = async (password: string) => {
	const passwordHash = await bcrypt.hash(password, saltRounds);
	return passwordHash;
};

const verifyPassword = async (password: string, hashedPassword: string) => {
	const isCorrect = await bcrypt.compare(password, hashedPassword);
	return isCorrect;
};

export { hashPassword, verifyPassword };
