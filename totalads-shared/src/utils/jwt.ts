import { default as jwt, JwtPayload } from "jsonwebtoken";
const { sign: jwtSign, verify: jwtVerify } = jwt;

const createJWT = (payload: object, expiresIn: number) =>
	new Promise<string>((resolve, reject) =>
		jwtSign(
			payload,
			process.env.JWT_SECRET as string,
			{
				algorithm: "HS256",
				expiresIn,
			},
			(err, token) => (err ? reject(err) : resolve(token as string)),
		),
	);

const verifyJWT = (token: string) =>
	new Promise<JwtPayload>((resolve, reject) =>
		jwtVerify(
			token,
			process.env.JWT_SECRET as string,
			{
				algorithms: ["HS256"],
			},
			(err, decoded) =>
				err ? reject(err) : resolve(decoded as JwtPayload),
		),
	);

export { createJWT, verifyJWT };
