/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextFunction, Request, RequestHandler, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { z, ZodError, ZodSchema, ZodTypeDef } from 'zod';

import { HTTPError } from '../config/error';

type ExpressAsyncHandler = {
	(
		fn: (
			req: Request<any, any, any, any>,
			res: Response<any, any>,
			next: NextFunction
		) => Promise<any>
	): RequestHandler;
	<Output, Input>(
		fn: (
			validatedBody: z.infer<ZodSchema<Output, ZodTypeDef, Input>>,
			req: Request<any, any, any, any>,
			res: Response<any, any>,
			next: NextFunction
		) => Promise<any>,
		options: {
			validationSchema: ZodSchema<Output, ZodTypeDef, Input>;
			getValue: (req: Request) => Promise<any> | any;
		}
	): RequestHandler;
};

export const expressAsyncHandler: ExpressAsyncHandler = (
	fn: any,
	options?: any
) => {
	return async (req: Request, res: Response, next: NextFunction) => {
		try {
			if (options) {
				const { validationSchema, getValue } = options;
				const value = await getValue(req);
				let validatedValue;
				try {
					validatedValue = await (
						validationSchema as ZodSchema
					).parseAsync(value);
				} catch (err) {
					throw new HTTPError({
						httpStatus:
							err instanceof ZodError
								? StatusCodes.UNPROCESSABLE_ENTITY
								: StatusCodes.INTERNAL_SERVER_ERROR,
						message:
							err instanceof ZodError
								? err.errors[0].message
								: undefined,
						reason:
							err instanceof ZodError ? err.format() : undefined,
					});
				}
				return await fn(validatedValue, req, res, next);
			} else return await fn(req, res, next);
		} catch (err) {
			console.log(err);
			return next(err);
		}
	};
};

export default expressAsyncHandler;
