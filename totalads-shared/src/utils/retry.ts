import AsyncRetry from "async-retry";
import asyncRetry, { RetryFunction } from "async-retry";

interface Options extends AsyncRetry.Options {
	name?: string;
}

type RetryResult<Res> =
	| {
			success: true;
			result: Awaited<Res>;
	  }
	| {
			success: false;
			result: null;
	  };

const retry = async <Res>(
	fn: RetryFunction<Res>,
	opts?: Options,
): Promise<RetryResult<Res>> => {
	let tries = 0;
	const startTime = new Date().getTime();
	try {
		const result = await asyncRetry<Res>(
			async (...args) => {
				tries++;
				return await fn(...args);
			},
			{
				onRetry: (err, attempt) =>
					console.info(
						`Retrying task${
							opts?.name ? ` ${opts.name}` : ""
						} which failed after ${attempt} attempts with this err -> ${err}`,
					),
				...opts,
			},
		);
		const endTime = new Date().getTime();
		const totalTimeInSeconds = (endTime - startTime) / 1000;
		console.log(
			`Task${
				opts?.name ? ` ${opts.name}` : ""
			} completed in ${totalTimeInSeconds} seconds after ${tries} tries.`,
		);
		return {
			success: true,
			result,
		};
	} catch (err) {
		const endTime = new Date().getTime();
		const totalTimeInSeconds = (endTime - startTime) / 1000;
		console.log(
			`Task${
				opts?.name ? ` ${opts.name}` : ""
			} failed after ${totalTimeInSeconds} seconds with a total of ${tries} attempts with err - ${err}.`,
		);
		return {
			success: false,
			result: null,
		};
	}
};

export default retry;
