import { GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import s3 from "../config/aws/s3";

const getS3PresignedGet = async (key: string) => {
	const command = new GetObjectCommand({
		Bucket: process.env.AWS_S3_BUCKET_NAME,
		Key: key,
	});
	const url = await getSignedUrl(s3, command, {
		expiresIn: 3600,
	});
	return url;
};

export default getS3PresignedGet;
