import { getSignedUrl } from "@aws-sdk/cloudfront-signer";

const getCloudFrontSignedURL = async (key: string) => {
	const url = getSignedUrl({
		url: `${process.env.CLOUDFRONT_DISTRIBUTION_DOMAIN_NAME}/${key}`,
		keyPairId: process.env.CLOUDFRONT_KEY_PAIR_ID as string,
		privateKey: process.env.CLOUDFRONT_PRIVATE_KEY as string,
		dateLessThan: `${new Date(Date.now() + 1000 * 60 * 60 * 24)}`,
	});
	return url;
};

export default getCloudFrontSignedURL;
