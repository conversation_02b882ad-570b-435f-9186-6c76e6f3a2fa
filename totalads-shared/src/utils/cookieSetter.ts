import { Response } from "express";

import { createJWT } from "./jwt.ts";

interface CookieSetterFromUserIdData {
  userId: bigint;
  cookieExpiryInSeconds: number;
  res: Response;
}

const cookieSetterFromUserId = async ({
  userId,
  cookieExpiryInSeconds,
  res,
}: CookieSetterFromUserIdData) => {
  const jwtToken = await createJWT(
    {
      id: `${userId}`,
    },
    cookieExpiryInSeconds
  );
  const cookieConfig =
    process.env.ENV === "prod"
      ? {
          httpOnly: true,
          maxAge: cookieExpiryInSeconds * 1000,
          domain: process.env.COOKIE_DOMAIN,
          secure: true,
        }
      : {
          httpOnly: true,
          maxAge: cookieExpiryInSeconds * 1000,
          secure: false,
        };
  res.cookie("auth", jwtToken, cookieConfig);
};

export default cookieSetterFromUserId;
