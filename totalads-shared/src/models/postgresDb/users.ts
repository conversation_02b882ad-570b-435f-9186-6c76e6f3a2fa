import {
	bigserial,
	boolean,
	pgTable,
	text,
	timestamp,
	uniqueIndex,
} from "drizzle-orm/pg-core";

export const users = pgTable(
	"users",
	{
		id: bigserial("id", {
			mode: "bigint",
		}).primaryKey(),
		name: text("name"),

		email: text("email").notNull(),
		accountCreatedAt: timestamp("account_created_at", {
			mode: "date",
		})
			.defaultNow()
			.notNull(),
		password: text("password").notNull(),
		emailVerified: boolean("email_verified").notNull().default(false),
	},
	(users) => ({
		emailIdx: uniqueIndex("idx_users_email").on(users.email),
	}),
);
