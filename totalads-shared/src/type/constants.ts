import { InternalDiscordNotificationTypes } from "./enum";

export const INTERNAL_DISCORD_NOTIFICATION_TYPES: Record<
  InternalDiscordNotificationTypes,
  { name: string; webhookURL: string }
> = {
  [InternalDiscordNotificationTypes.GENERAL]: {
    name: "General notifications",
    webhookURL:
      "https://discord.com/api/webhooks/1341852425245691975/-xEzL8THQl267v15irhTK3ubeMhe27TzXFVdiM2iFZ7uO3aSS4cvRRVJrggzsseLUlHW",
  },
  [InternalDiscordNotificationTypes.FAILURE]: {
    name: "Failure notifications",
    webhookURL:
      "https://discord.com/api/webhooks/1341852425245691975/-xEzL8THQl267v15irhTK3ubeMhe27TzXFVdiM2iFZ7uO3aSS4cvRRVJrggzsseLUlHW",
  },
};
