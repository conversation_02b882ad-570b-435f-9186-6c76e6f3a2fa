import dotenv from "dotenv";
import process from "process";

const loadEnv = (envFileLocation?: string) => {
	const envPath =
		envFileLocation ||
		(process.env.ENV?.toLowerCase() === "prod" ? ".env" : ".env.local");
	const loadedEnv = dotenv.config({
		path: envPath,
	});
	process.env = {
		ENV: process.env.ENV,
		...loadedEnv.parsed,
	};
	console.log(`.env resolved from ${envPath}`);
};

export default loadEnv;
