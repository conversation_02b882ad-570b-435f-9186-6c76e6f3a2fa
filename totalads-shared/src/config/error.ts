import { StatusCodes } from "http-status-codes";

interface HTTPErrorData {
  httpStatus: StatusCodes;
  message?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  reason?: any; // anything JSON parsable
}

export class HTTPError extends Error {
  httpStatus: StatusCodes;
  reason?: object | string;

  constructor({ httpStatus, message, reason }: HTTPErrorData) {
    super(message);
    this.httpStatus = httpStatus;
    this.reason = reason;
  }
}
