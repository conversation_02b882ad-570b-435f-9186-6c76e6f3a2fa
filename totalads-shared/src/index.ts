// global utils

export { default as cookieSetterFromUserId } from "./utils/cookieSetter.ts";
export { default as expressAsyncHandler } from "./utils/expressAsyncHandler.ts";
export { default as getCloudFrontSignedURL } from "./utils/getCloudFrontSignedURL.ts";
export { default as getS3PresignedGet } from "./utils/getS3PresignedGet.ts";
export { hashPassword, verifyPassword } from "./utils/hashingTools.ts";
export { createJWT, verifyJWT } from "./utils/jwt.ts";
export { default as sendEmail } from "./utils/sendEmail.ts";
export { default as sleep } from "./utils/sleep.ts";
export { default as parsePhoneNumber } from "./utils/parsePhoneNumber.ts";
export { default as swaggerAutogen } from "./utils/swaggerAutogen.ts";
export { default as retry } from "./utils/retry.ts";

// services

export { Service, TaskQueueService } from "./services/index.ts";
export {
	default as NotificationSenderService,
	NotificationSenderIn,
} from "./services/notificationSender.ts";

// AI services
export {
	aiService,
	AIService,
	type BusinessIntelligence,
	type EnhancedContactInfo,
	type AIProcessingResult,
} from "./services/aiService.ts";
export {
	aiCostMonitor,
	AICostMonitor,
	type CostAlert,
	type CostBudget,
	type UsageStats,
} from "./services/aiCostMonitor.ts";

// db utils

export { default as incrementColumn } from "./utils/dbUtils/incrementColumn.ts";
export { default as decrementColumn } from "./utils/dbUtils/decrementColumn.ts";
export { default as jsonbSetter } from "./utils/dbUtils/jsonbSetter.ts";

// postgresdb models
export { users } from "./models/postgresDb/users.ts";

// config

export { default as loadEnv } from "./config/loadEnv.ts";
export { default as s3, S3_BUCKET_NAME } from "./config/aws/s3.ts";
export { default as notificationSenderService } from "./config/queues/notificationSender.ts";
export {
	aiClient,
	AIClientManager,
	type AIConfig,
	type TokenUsage,
	type CachedResponse,
} from "./config/ai.ts";
export { INTERNAL_DISCORD_NOTIFICATION_TYPES } from "./type/constants.ts";
export {
	TaskQueueServiceType,
	TaskQueueServiceInitializationMode,
	InternalDiscordNotificationTypes,
	NotificationType,
	UserSentiment,
	BatchRunMethod,
	AllowedMappedToColumns,
} from "./type/enum.ts";
export { default as ErrorMessages } from "./config/errorMessages.ts";
export { HTTPError } from "./config/error.ts";
export { PreJobQueueSyncHandleIn, AddBulkJob } from "./type/interface.ts";
export {} from "./type/type.ts";
export {
	emailValidationSchema,
	passwordValidationSchema,
	getMinMaxValidationSchema,
	getStringValidationSchema,
	getBigintValidationSchema,
	getBooleanValidationSchema,
	getNumberValidationSchema,
} from "./config/zodSchemas.ts";
export { default as postgresDb } from "./config/postgresDb";
export { default as redisClient, createRedisClient } from "./config/redis.ts";

// express stuff

export { default as Server } from "./express/server.ts";
export { default as authMiddlewareCreator } from "./express/middlewares/authMiddleware.ts";
