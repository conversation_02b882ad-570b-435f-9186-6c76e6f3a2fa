import { Job, JobsOptions, Queue, QueueOptions, Worker, WorkerOptions } from 'bullmq';
import { StatusCodes } from 'http-status-codes';

import { HTTPError } from '../config/error.ts';
import postgresDb from '../config/postgresDb.ts';
import { createRedisClient } from '../config/redis.ts';
import { TaskQueueServiceInitializationMode, TaskQueueServiceType } from '../type/enum.ts';
import { AddBulkJob, PreJobQueueSyncHandleIn } from '../type/interface.ts';

abstract class Service<Data, Response> {
	data: Data;

	constructor(data: Data) {
		this.data = data;
	}

	async validate(): Promise<string | undefined> {
		return undefined;
	}

	abstract handle(): Promise<Response>;

	async execute(): Promise<Response> {
		const validationErr = await this.validate();
		if (validationErr)
			throw new HTTPError({
				httpStatus: StatusCodes.UNPROCESSABLE_ENTITY,
				message: validationErr,
			});
		return await this.handle();
	}

	async executeWithoutValidation(): Promise<Response> {
		return await this.handle();
	}
}

type TaskQueueServiceInitData =
	| {
			initializationMode: TaskQueueServiceInitializationMode.QUEUE;
			queueOptions?: QueueOptions;
	  }
	| {
			initializationMode: TaskQueueServiceInitializationMode.WORKER;
			workerOptions?: WorkerOptions;
	  };

abstract class TaskQueueService<Data, Response> {
	worker?: Worker<Data, Response>;
	queue?: Queue<Data, Response>;
	queueServiceType: TaskQueueServiceType = TaskQueueServiceType.BOTH;

	abstract getQueueName(): string;

	constructor(initData: TaskQueueServiceInitData) {
		const { initializationMode } = initData;
		const redisClient = createRedisClient({
			lazyConnect: false,
		});
		if (initializationMode === TaskQueueServiceInitializationMode.QUEUE) {
			if (
				![
					TaskQueueServiceType.QUEUE,
					TaskQueueServiceType.BOTH,
				].includes(this.queueServiceType)
			)
				throw Error(
					"You can't initialize this service as a queue if it's not a QUEUE or BOTH TaskQueueServiceType."
				);
			this.queue = new Queue<Data, Response>(this.getQueueName(), {
				connection: redisClient,
				...initData.queueOptions,
			});
		} else {
			if (
				![
					TaskQueueServiceType.WORKER,
					TaskQueueServiceType.BOTH,
				].includes(this.queueServiceType)
			)
				throw Error(
					"You can't initialize this service as a worker if it's not a WORKER or BOTH TaskQueueServiceType."
				);
			this.worker = new Worker<Data, Response>(
				this.getQueueName(),
				this.handleWithHooks.bind(this),
				{
					connection: redisClient,
					concurrency: 20,
					...initData.workerOptions,
				}
			);
			console.log(
				`Worker started for processing tasks in queue ${this.getQueueName()}.`
			);
			this.attachStandardEventListeners();
			console.log(`Standard event listeners attached to the worker.`);
		}
	}

	private async attachStandardEventListeners() {
		const worker = this.worker as Worker<Data, Response>;
		worker.on("active", (job) => {
			console.log(`Executing job: ${job.id}`);
		});
		worker.on("completed", (job) => {
			const jobDurationInSecs =
				((job.finishedOn as number) - job.timestamp) / 1000;
			console.log(
				`Job: ${job.id} executed successfully in ${jobDurationInSecs} seconds after ${job.attemptsMade} attempts.`
			);
		});
		worker.on("failed", (job, err) => {
			const jobObject = job as Job<Data, Response, string>;
			const jobDurationInSecs =
				((jobObject.finishedOn as number) - jobObject.timestamp) / 1000;
			console.error(
				`Job: ${
					jobObject.id
				} failed in ${jobDurationInSecs} seconds after ${
					jobObject.attemptsMade
				} attempts with this error: ${JSON.stringify(
					err
				)} and this and this stacktrace: ${jobObject.stacktrace.join(
					"\n"
				)}.`
			);
		});
	}

	async runPreJobQueueSyncHandle(data: Data) {
		if (this.queue)
			return postgresDb.transaction(
				async (db) =>
					await this.preJobQueueSyncHandle({
						name: "",
						data,
						db,
					})
			);
		else
			throw new Error(
				"TaskQueueService should be initialized as a queue before running preJobQueueSyncHandle."
			);
	}

	async addJob(name: string, data: Data, options?: JobsOptions) {
		if (this.queue)
			return postgresDb.transaction(async (db) => {
				await this.preJobQueueSyncHandle({ name, data, options, db });
				return await (this.queue as any)?.add(name, data, options);
			});
		else
			throw new Error(
				"TaskQueueService should be initialized as a queue before adding a job."
			);
	}

	async addBulkJob(jobs: AddBulkJob<Data>[]) {
		if (this.queue)
			return postgresDb.transaction(async (db) => {
				const preJobQueueSyncHandlePromises = jobs.map(
					({ name, data, opts }) =>
						this.preJobQueueSyncHandle({
							name,
							data,
							options: opts,
							db,
						})
				);
				await Promise.all(preJobQueueSyncHandlePromises);
				return await (this.queue as any).addBulk(jobs);
			});
		else
			throw new Error(
				"TaskQueueService should be initialized as a queue before adding bulk jobs."
			);
	}

	async getJob(jobId: string) {
		if (this.queue) return await this.queue.getJob(jobId);
		else
			throw new Error(
				"TaskQueueService should be initialized as a queue before getting status of a job."
			);
	}

	async deleteJob(jobId: string) {
		if (this.queue) return await this.queue.remove(jobId);
		else
			throw new Error(
				"TaskQueueService should be initialized as a queue before deleting a job."
			);
	}

	async preJobQueueSyncHandle({
		name,
		data,
		db,
		options,
	}: {
		name: string;
		data: Data;
		db: typeof postgresDb;
		options?: JobsOptions;
	}): Promise<void> {
		return;
	}

	private async handleWithHooks(job: Job<Data, Response>) {
		try {
			await this.onStart(job);
			const result = await this.handle(job);
			await this.onSuccess(job, result);
			return result;
		} catch (err) {
			await this.onError(job, err as Error);
			throw err;
		}
	}

	async onStart(job: Job<Data, Response>) {}

	async onSuccess(job: Job<Data, Response>, result: Response) {}

	async onError(job: Job<Data, Response>, error: Error) {}

	protected abstract handle(job: Job<Data, Response>): Promise<Response>;
}

export { Service, TaskQueueService };
