import { AxiosError } from "axios";
import { Job } from "bullmq";
import { StatusCodes } from "http-status-codes";

import { INTERNAL_DISCORD_NOTIFICATION_TYPES } from "../type/constants.ts";
import {
  InternalDiscordNotificationTypes,
  NotificationType,
} from "../type/enum.ts";
import DiscordLogger from "../utils/discordLogger.ts";
import retry from "../utils/retry.ts";
import sendEmail from "../utils/sendEmail.ts";
import { TaskQueueService } from "./index.ts";

export type NotificationSenderIn =
  | {
      type: NotificationType.EMAIL;
      data: {
        email: string;
        subject: string;
        html: string;
      };
    }
  | {
      type: NotificationType.INTERNAL_DISCORD;
      data: {
        discordNotificationType: InternalDiscordNotificationTypes;
        logLevel: "error" | "warn" | "info" | "verbose" | "debug" | "silly";
        logData: {
          message: string;
          description?: string;
          error?: Error;
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          json?: any;
          meta?: {
            [key: string]: string | number | Date;
          };
        };
      };
    };

class NotificationSenderService extends TaskQueueService<
  NotificationSenderIn,
  void
> {
  getQueueName(): string {
    return "notification-sender";
  }

  async handle(job: Job<NotificationSenderIn, void>) {
    const { data, type } = job.data;
    if (type === NotificationType.EMAIL) await sendEmail(data);
    else {
      await retry(async () => {
        try {
          const { name, webhookURL } =
            INTERNAL_DISCORD_NOTIFICATION_TYPES[data.discordNotificationType];
          const logger = new DiscordLogger({
            hook: webhookURL,
            serviceName: name,
          });
          await logger[data.logLevel](data.logData);
        } catch (err) {
          if (
            err instanceof AxiosError &&
            err.response?.status === StatusCodes.TOO_MANY_REQUESTS
          )
            throw err;
        }
      });
    }
  }
}

export default NotificationSenderService;
