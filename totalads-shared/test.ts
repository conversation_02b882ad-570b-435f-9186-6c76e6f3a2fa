// loadEnv();
// import { and, count, eq, gt, inArray, sql } from "drizzle-orm";
// import {
//     campaigns,
//     companyCommunicationChannelsEnabled,
//     companyDetails,
//     companyiMessageChannelDetails,
//     companyUserAssignedTags,
//     companyUserCampaignMappings,
//     companyUsers,
//     companyUserTags,
//     companyWhatsappChannelDetails,
//     companyWhatsappTemplates,
//     jobs,
//     jsonbSetter,
//     loadEnv,
//     memory,
//     memoryContexts,
//     personas,
//     PersonaTones,
//     messages,
//     MessageSenderType,
//     MessageStatuses,
//     openai,
//     postgresDb,
//     sharedCompanies,
//     users,
// } from "./src";

/*
1. Remove firstName and lastName column from users table.
2. Remove companies that don't make any sense
3. Create one persona and use that for next steps
4. Remove overrided persona column and make persona column required in campaigns table
5. Spread second brain data to company details table directly and delete second brain column
*/

// const companiesToDelete = [
//     3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23,
//     24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43,
//     44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62,
//     63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81,
//     86, 96, 108, 112, 113, 115, 116, 117,
// ].map(BigInt);

// const migrate = async () => {
//     await postgresDb.transaction(async (db) => {
//         await db.update(users).set({
//             name: sql`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
//         });
//         console.log("Users name shit done!");
//         await Promise.all([
//             db
//                 .delete(campaigns)
//                 .where(inArray(campaigns.companyId, companiesToDelete)),
//             db
//                 .delete(companyCommunicationChannelsEnabled)
//                 .where(
//                     inArray(
//                         companyCommunicationChannelsEnabled.companyId,
//                         companiesToDelete
//                     )
//                 ),
//             db
//                 .delete(companyiMessageChannelDetails)
//                 .where(
//                     inArray(
//                         companyiMessageChannelDetails.companyId,
//                         companiesToDelete
//                     )
//                 ),
//             db
//                 .delete(companyUserAssignedTags)
//                 .where(
//                     inArray(
//                         companyUserAssignedTags.tagId,
//                         sql.raw(
//                             `(SELECT company_user_tags.id FROM company_user_tags WHERE company_user_tags.company_id IN (${companiesToDelete.join(
//                                 ", "
//                             )}))`
//                         )
//                     )
//                 ),
//             db
//                 .delete(companyUsers)
//                 .where(inArray(companyUsers.companyId, companiesToDelete)),
//             db
//                 .delete(companyUserTags)
//                 .where(inArray(companyUserTags.companyId, companiesToDelete)),
//             db
//                 .delete(companyWhatsappChannelDetails)
//                 .where(
//                     inArray(
//                         companyWhatsappChannelDetails.companyId,
//                         companiesToDelete
//                     )
//                 ),
//             db
//                 .delete(companyWhatsappTemplates)
//                 .where(
//                     inArray(
//                         companyWhatsappTemplates.companyId,
//                         companiesToDelete
//                     )
//                 ),
//             db.delete(jobs).where(inArray(jobs.companyId, companiesToDelete)),
//             db
//                 .delete(memory)
//                 .where(inArray(memory.companyId, companiesToDelete)),
//             db
//                 .delete(memoryContexts)
//                 .where(inArray(memoryContexts.companyId, companiesToDelete)),
//             db
//                 .delete(sharedCompanies)
//                 .where(inArray(sharedCompanies.companyId, companiesToDelete)),
//             db
//                 .delete(companyDetails)
//                 .where(inArray(companyDetails.id, companiesToDelete)),
//         ]);
//         console.log("Fake companies deleted!");
//         const companies = await db
//             .select({
//                 id: companyDetails.id,
//                 secondBrain: companyDetails.secondBrain,
//             })
//             .from(companyDetails);
//         const createdPersonas = await postgresDb
//             .insert(personas)
//             .values(
//                 companies.map(({ id, secondBrain }) => ({
//                     companyId: id,
//                     name: secondBrain.persona.name,
//                     position: secondBrain.persona.position,
//                     description: "N/A",
//                     tone: PersonaTones.PROFESSIONAL,
//                 }))
//             )
//             .returning({
//                 id: personas.id,
//                 companyId: personas.companyId,
//             });
//         console.log("Default personas created");
//         await Promise.all(
//             createdPersonas.map(async ({ id, companyId }) => {
//                 const { secondBrain } = companies.find(
//                     ({ id }) => id === companyId
//                 )!;
//                 await db
//                     .update(companyDetails)
//                     .set({
//                         aboutBusiness: secondBrain.aboutBusiness,
//                         defaultChatPrompt: secondBrain.defaultChatPrompt,
//                         defaultPersona: id,
//                         companyWebsiteURL: secondBrain.companyWebsiteURL,
//                         companySocialMediaURLs: jsonbSetter(
//                             secondBrain.companySocialMediaURLs
//                         ),
//                     })
//                     .where(eq(companyDetails.id, companyId));
//             })
//         );
//         console.log(
//             "Second brain data migrated to columns in company details table"
//         );
//         const companiesResult = await db
//             .select({
//                 id: companyDetails.id,
//                 persona: companyDetails.defaultPersona,
//             })
//             .from(companyDetails);
//         await Promise.all(
//             companiesResult.map(async ({ id, persona }) => {
//                 if (persona) {
//                     await db
//                         .update(campaigns)
//                         .set({
//                             persona,
//                         })
//                         .where(eq(campaigns.companyId, id));
//                 }
//             })
//         );
//     });
//     console.log("DONE");
// };

// const script = async () => {
//     const result = await postgresDb
//         .select({
//             ids: sql<bigint[]>`ARRAY_AGG(${companyUsers.id})`,
//             name: sql<string>`(ARRAY_AGG(${companyUsers.name}))[1]`,
//             phoneNumber: companyUsers.phoneNumber,
//             occurrences: count(),
//         })
//         .from(companyUsers)
//         .groupBy(companyUsers.companyId, companyUsers.phoneNumber)
//         .having(gt(count(), 1));
//     const finalResult = await Promise.all(
//         result.map(async (row) => {
//             const occurrences = await postgresDb.execute<{
//                 id: string;
//                 reverted: true;
//             }>(
//                 sql.raw(`SELECT
//             company_users.id,
//             messages_query.reverted
//         FROM
//             company_users
//             LEFT JOIN LATERAL (
//                 SELECT
//                     COUNT(*) > 0 AS reverted
//                 FROM
//                     messages
//                 WHERE
//                     messages.company_user_id = company_users.id
//                     AND messages.sender_type = 1) messages_query ON TRUE
//         WHERE
//             company_users.id IN (${row.ids.join(", ")});`)
//             );
//             return {
//                 ids: occurrences,
//                 name: row.name,
//                 phoneNumber: row.phoneNumber,
//             };
//         })
//     );
//     const userIdsToDelete: bigint[] = [];
//     finalResult.forEach((row) => {
//         const noRevertedUsersForPhoneNumber = row.ids.every(
//             ({ reverted }) => !reverted
//         );
//         if (noRevertedUsersForPhoneNumber) {
//             userIdsToDelete.push(
//                 ...row.ids.slice(1).map(({ id }) => BigInt(id))
//             );
//         } else {
//             userIdsToDelete.push(
//                 ...row.ids
//                     .filter(({ reverted }) => !reverted)
//                     .map(({ id }) => BigInt(id))
//             );
//         }
//     });
//     console.log(userIdsToDelete);
//     console.log(userIdsToDelete.length);
//     await postgresDb.transaction(async (db) => {
//         await Promise.all([
//             db
//                 .delete(messages)
//                 .where(inArray(messages.companyUserId, userIdsToDelete)),
//             db
//                 .delete(companyUserWhatsappChannelDetails)
//                 .where(
//                     inArray(
//                         companyUserWhatsappChannelDetails.companyUserId,
//                         userIdsToDelete
//                     )
//                 ),
//             db
//                 .delete(companyUserAssignedTags)
//                 .where(
//                     inArray(
//                         companyUserAssignedTags.companyUserId,
//                         userIdsToDelete
//                     )
//                 ),
//             db
//                 .delete(companyUserCampaignMappings)
//                 .where(
//                     inArray(
//                         companyUserCampaignMappings.companyUserId,
//                         userIdsToDelete
//                     )
//                 ),
//             db
//                 .delete(companyUsers)
//                 .where(inArray(companyUsers.id, userIdsToDelete)),
//         ]);
//     });
//     console.log("DONE");
//     // let length = 0;
//     // finalResult.forEach((row) => {
//     //     length += row.ids.filter((id) => id.reverted).length;
//     //     if (row.ids.filter((id) => id.reverted).length > 1) console.log(row);
//     // });
//     // console.log(length);
// };
