import { defineConfig } from "tsup";

import loadEnv from "./src/config/loadEnv.ts";

loadEnv();

const isValidId = (str: string) => {
	try {
		new Function(`let ${str};`);
	} catch {
		return false;
	}
	return true;
};
const env: Record<string, string> = {};
Object.keys(process.env).forEach((key) => {
	const value = process.env[key];
	if (isValidId(key) && value) env[key] = value;
});

export default defineConfig({
	format: "esm",
	dts: true,
	shims: true,
	env,
});
