import { Router } from 'express';
import { authMiddlewareCreator, expressAsyncHandler } from 'totalads-shared';

import UpdateUserService, { UpdateUserDataSchema } from '../../services/auth/updateUser';
import GetUserInfoService from '../../services/users/info';

const usersRouter = Router();

usersRouter.get(
	"/me",
	authMiddlewareCreator(),
	expressAsyncHandler(async (req, res) => {
		const getUserInfoService = new GetUserInfoService({
			userId: req.userId,
		});
		const userInfo = await getUserInfoService.execute();
		return res.status(200).send(userInfo);
	}),
);

usersRouter.patch(
	"/me",
	authMiddlewareCreator(),
	expressAsyncHandler(
		async (validatedData, req, res) => {
			const updateUserService = new UpdateUserService({
				userId: req.userId,
				...validatedData,
			});
			await updateUserService.execute();
			return res.status(200).send({
				message: "User updated successfully!",
			});
		},
		{
			validationSchema: UpdateUserDataSchema,
			getValue: (req) => ({
				name: req.body.name,
				email: req.body.email,
			}),
		},
	),
);

export default usersRouter;
