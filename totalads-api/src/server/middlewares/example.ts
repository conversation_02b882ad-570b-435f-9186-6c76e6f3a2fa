// import { eq } from "drizzle-orm";
// import { NextFunction, Request, Response } from "express";
// import { StatusCodes } from "http-status-codes";
// import { HTTPError, personas, postgresDb } from "totalads-shared";

// const companyPersonaValidationMiddleware = async (
// 	req: Request<{
// 		companyId: string;
// 		personaId: string;
// 	}>,
// 	res: Response,
// 	next: NextFunction,
// ) => {
// 	let companyId, personaId;
// 	try {
// 		companyId = BigInt(req.params.companyId);
// 	} catch {
// 		return next(
// 			new HTTPError({
// 				httpStatus: StatusCodes.UNPROCESSABLE_ENTITY,
// 				message: "companyId is invalid",
// 			}),
// 		);
// 	}
// 	try {
// 		personaId = BigInt(req.params.personaId);
// 	} catch {
// 		return next(
// 			new HTTPError({
// 				httpStatus: StatusCodes.UNPROCESSABLE_ENTITY,
// 				message: "personaId is invalid",
// 			}),
// 		);
// 	}
// 	const [persona] = await postgresDb
// 		.select({
// 			companyId: personas.companyId,
// 		})
// 		.from(personas)
// 		.where(eq(personas.id, personaId))
// 		.limit(1);
// 	return next(
// 		persona
// 			? persona.companyId === companyId
// 				? undefined
// 				: new HTTPError({ httpStatus: StatusCodes.FORBIDDEN })
// 			: new HTTPError({
// 					httpStatus: StatusCodes.NOT_FOUND,
// 				}),
// 	);
// };

// export default companyPersonaValidationMiddleware;
