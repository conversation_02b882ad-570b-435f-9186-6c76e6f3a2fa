import { eq } from 'drizzle-orm';
import { postgresDb, Service, users } from 'totalads-shared';

export interface GetUserInfoServiceData {
	userId: bigint;
}

interface GetUserInfoServiceOut {
	id: string;
	name: string;
	email: string;
	userType: number;
	emailVerified: boolean;
}

class GetUserInfoService extends Service<
	GetUserInfoServiceData,
	GetUserInfoServiceOut
> {
	async handle(): Promise<GetUserInfoServiceOut> {
		const { id, ...user } = await postgresDb.transaction(async (db) => {
			const result = await db
				.select({
					id: users.id,
					name: users.name,
					email: users.email,
					emailVerified: users.emailVerified,
				})
				.from(users)
				.where(eq(users.id, this.data.userId))
				.limit(1);
			const user = result[0]!;
			return {
				...user,
				name: user.name!,
			};
		});
		return {
			id: id.toString(),
			...user,
		};
	}
}

export default GetUserInfoService;
