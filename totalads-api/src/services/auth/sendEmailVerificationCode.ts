import { randomBytes } from 'crypto';
import { eq } from 'drizzle-orm';
import { StatusCodes } from 'http-status-codes';
import {
    ErrorMessages, HTTPError, notificationSenderService, NotificationType, postgresDb, Service,
    userEmailVerificationCodes, users
} from 'totalads-shared';

export interface SendEmailVerificationCodeData {
	userId: bigint;
}

class SendEmailVerificationCodeService extends Service<
	SendEmailVerificationCodeData,
	undefined
> {
	async handle(): Promise<undefined> {
		const { email, emailVerificationCode } = await postgresDb.transaction(
			async (db) => {
				const result = await db
					.select({
						email: users.email,
						emailVerified: users.emailVerified,
					})
					.from(users)
					.where(eq(users.id, this.data.userId))
					.limit(1);
				const user = result[0]!;
				if (user.emailVerified)
					throw new HTTPError({
						httpStatus: StatusCodes.BAD_REQUEST,
						message: ErrorMessages.EMAIL_ALREADY_VERIFIED,
					});
				const emailVerificationCode = randomBytes(32).toString("hex");
				await db
					.insert(userEmailVerificationCodes)
					.values({
						userId: this.data.userId,
						verificationCode: emailVerificationCode,
					})
					.onConflictDoUpdate({
						target: users.id,
						set: {
							verificationCode: emailVerificationCode,
							sentAt: new Date(new Date().toUTCString()),
						},
					});
				return {
					email: user.email,
					emailVerificationCode,
				};
			},
		);
		await notificationSenderService.addJob("user-verification-email", {
			type: NotificationType.EMAIL,
			data: {
				email: email,
				subject: "Email Verification",
				html: `Please click <a href="${process.env.PORTAL_ROOT_URL}verify-email?verification_token=${emailVerificationCode}">here</a> to verify your email.`,
			},
		});
	}
}

export default SendEmailVerificationCodeService;
