import { eq } from 'drizzle-orm';
import {
    getStringValidationSchema, postgresDb, Service, userResetPasswordCodes, users
} from 'totalads-shared';
import { z } from 'zod';

export const ValidateResetPasswordCodeDataSchema = z.object({
	code: getStringValidationSchema("code"),
});

export type ValidateResetPasswordCodeData = z.infer<
	typeof ValidateResetPasswordCodeDataSchema
>;

type ValidateResetPasswordCodeOut =
	| {
			isValid: false;
			user: null;
	  }
	| {
			isValid: true;
			user: {
				name: string;
			};
	  };

class ValidateResetPasswordCodeService extends Service<
	ValidateResetPasswordCodeData,
	ValidateResetPasswordCodeOut
> {
	async handle(): Promise<ValidateResetPasswordCodeOut> {
		const [result] = await postgresDb
			.select({
				user: {
					name: users.name,
				},
				sentAt: userResetPasswordCodes.sentAt,
			})
			.from(userResetPasswordCodes)
			.innerJoin(users, eq(users.id, userResetPasswordCodes.userId))
			.where(eq(userResetPasswordCodes.resetPasswordCode, this.data.code))
			.limit(1);
		if (!result)
			return {
				isValid: false,
				user: null,
			};
		const currentTimeInMs = new Date(new Date().toUTCString()).getTime();
		const sentAtTimeInMs = result.sentAt.getTime();
		if (
			process.env.RESET_PASSWORD_EMAIL_VALIDITY_IN_SECONDS &&
			(currentTimeInMs - sentAtTimeInMs) / 1000 >
				+process.env.RESET_PASSWORD_EMAIL_VALIDITY_IN_SECONDS
		)
			return {
				isValid: false,
				user: null,
			};
		return {
			isValid: true,
			user: {
				name: result.user.name!,
			},
		};
	}
}

export default ValidateResetPasswordCodeService;
