import { eq } from 'drizzle-orm';
import { StatusCodes } from 'http-status-codes';
import {
    ErrorMessages, getStringValidationSchema, hashPassword, HTTPError,
    InternalDiscordNotificationTypes, notificationSenderService, NotificationType,
    passwordValidationSchema, postgresDb, Service, userResetPasswordCodes, users
} from 'totalads-shared';
import { z } from 'zod';

export const ResetPasswordDataSchema = z.object({
	code: getStringValidationSchema("code"),
	newPassword: passwordValidationSchema,
});

export type ResetPasswordData = z.infer<typeof ResetPasswordDataSchema>;

class ResetPasswordService extends Service<ResetPasswordData, undefined> {
	async handle(): Promise<undefined> {
		await postgresDb.transaction(async (db) => {
			const [result] = await db
				.select({
					userId: userResetPasswordCodes.userId,
					firstName: users.firstName,
					lastName: users.lastName,
					email: users.email,
					sentAt: userResetPasswordCodes.sentAt,
				})
				.from(userResetPasswordCodes)
				.innerJoin(users, eq(users.id, userResetPasswordCodes.userId))
				.where(
					eq(
						userResetPasswordCodes.resetPasswordCode,
						this.data.code,
					),
				)
				.limit(1);
			const InvalidResetPasswordCodeError = new HTTPError({
				httpStatus: StatusCodes.BAD_REQUEST,
				message: ErrorMessages.INVALID_RESET_PASSWORD_CODE,
			});
			if (!result) throw InvalidResetPasswordCodeError;
			const currentTimeInMs = new Date(
				new Date().toUTCString(),
			).getTime();
			const sentAtTimeInMs = result.sentAt.getTime();
			if (
				process.env.RESET_PASSWORD_EMAIL_VALIDITY_IN_SECONDS &&
				(currentTimeInMs - sentAtTimeInMs) / 1000 >
					+process.env.RESET_PASSWORD_EMAIL_VALIDITY_IN_SECONDS
			)
				throw InvalidResetPasswordCodeError;
			await db
				.update(users)
				.set({
					password: await hashPassword(this.data.newPassword),
				})
				.where(eq(users.id, result.userId));
			await notificationSenderService.addJob(
				"user-password-reset-success",
				{
					type: NotificationType.INTERNAL_DISCORD,
					data: {
						discordNotificationType:
							InternalDiscordNotificationTypes.GENERAL,
						logLevel: "info",
						logData: {
							message: "Password reset successful for user",
							json: {
								name: result.firstName + " " + result.lastName,
								email: result.email,
							},
						},
					},
				},
			);
		});
	}
}

export default ResetPasswordService;
