import { randomBytes } from 'crypto';
import { eq } from 'drizzle-orm';
import { StatusCodes } from 'http-status-codes';
import {
    emailValidationSchema, ErrorMessages, hashPassword, HTTPError, notificationSenderService,
    NotificationType, passwordValidationSchema, postgresDb, Service, userEmailVerificationCodes,
    users
} from 'totalads-shared';
import { z } from 'zod';

export const SignupDataSchema = z.object({
	email: emailValidationSchema,
	password: passwordValidationSchema,
});

export type SignupData = z.infer<typeof SignupDataSchema>;

class SignupService extends Service<SignupData, bigint> {
	async handle(): Promise<bigint> {
		const userId = await postgresDb.transaction(async (db) => {
			const userSearchResult = await db
				.select({
					id: users.id,
				})
				.from(users)
				.where(eq(users.email, this.data.email))
				.limit(1);
			if (userSearchResult.length)
				throw new HTTPError({
					httpStatus: StatusCodes.UNAUTHORIZED,
					message: ErrorMessages.ACCOUNT_ALREADY_EXISTS,
				});
			const hashedPassword = await hashPassword(this.data.password);
			const result = await db
				.insert(users)
				.values({
					email: this.data.email,
					password: hashedPassword,
				})
				.returning({
					id: users.id,
				});
			const user = result[0]!;
			const userId = user.id;
			const emailVerificationCode = randomBytes(32).toString("hex");
			await db.insert(userEmailVerificationCodes).values({
				userId,
				verificationCode: emailVerificationCode,
			});
			notificationSenderService.addJob("user-verification-email", {
				type: NotificationType.EMAIL,
				data: {
					email: this.data.email,
					subject: "Email Verification",
					html: `Please click <a href="${process.env.PORTAL_ROOT_URL}verify-email?verification_token=${emailVerificationCode}">here</a> to verify your email.`,
				},
			});
			return userId;
		});
		return userId;
	}
}

export default SignupService;
