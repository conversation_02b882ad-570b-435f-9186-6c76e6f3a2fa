import { eq } from 'drizzle-orm';
import { StatusCodes } from 'http-status-codes';
import {
    ErrorMessages, getStringValidationSchema, HTTPError, postgresDb, Service,
    userEmailVerificationCodes, users
} from 'totalads-shared';
import { z } from 'zod';

export const VerifyEmailWithoutAuthDataSchema = z.object({
	code: getStringValidationSchema("code"),
});

export type VerifyEmailWithoutAuthData = z.infer<
	typeof VerifyEmailWithoutAuthDataSchema
>;

export type VerifyEmailData = {
	userId: bigint;
} & VerifyEmailWithoutAuthData;

class VerifyEmailService extends Service<VerifyEmailData, undefined> {
	async handle(): Promise<undefined> {
		await postgresDb.transaction(async (db) => {
			const result = await db
				.select({
					emailVerified: users.emailVerified,
				})
				.from(users)
				.where(eq(users.id, this.data.userId))
				.limit(1);
			const user = result[0]!;
			if (user.emailVerified)
				throw new HTTPError({
					httpStatus: StatusCodes.BAD_REQUEST,
					message: ErrorMessages.EMAIL_ALREADY_VERIFIED,
				});
			const [emailVerificationCode] = await db
				.select({
					verificationCode:
						userEmailVerificationCodes.verificationCode,
					sentAt: userEmailVerificationCodes.sentAt,
				})
				.from(userEmailVerificationCodes)
				.where(eq(userEmailVerificationCodes.userId, this.data.userId))
				.limit(1);
			const InvalidEmailVerificationCodeError = new HTTPError({
				httpStatus: StatusCodes.BAD_REQUEST,
				message: ErrorMessages.INVALID_EMAIL_VERIFICATION_CODE,
			});
			if (
				!emailVerificationCode ||
				emailVerificationCode.verificationCode !== this.data.code
			)
				throw InvalidEmailVerificationCodeError;
			const currentTime = new Date(new Date().toUTCString());
			const currentTimeInMs = currentTime.getTime();
			const sentAtTimeInMs = emailVerificationCode.sentAt.getTime();
			if (
				process.env.VERIFICATION_EMAIL_VALIDITY_IN_SECONDS &&
				(currentTimeInMs - sentAtTimeInMs) / 1000 >
					+process.env.VERIFICATION_EMAIL_VALIDITY_IN_SECONDS
			)
				throw InvalidEmailVerificationCodeError;
			await db
				.update(users)
				.set({
					emailVerified: true,
				})
				.where(eq(users.id, this.data.userId));
		});
	}
}

export default VerifyEmailService;
