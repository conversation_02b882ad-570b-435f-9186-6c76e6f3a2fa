import { eq } from 'drizzle-orm';
import { StatusCodes } from 'http-status-codes';
import {
    emailValidationSchema, ErrorMessages, getStringValidationSchema, HTTPError, postgresDb, Service,
    users, verifyPassword
} from 'totalads-shared';
import { z } from 'zod';

export const LoginDataSchema = z.object({
	email: emailValidationSchema,
	password: getStringValidationSchema("password"),
});

export type LoginData = z.infer<typeof LoginDataSchema>;

class LoginService extends Service<LoginData, bigint> {
	async handle(): Promise<bigint> {
		const [user] = await postgresDb
			.select({
				id: users.id,
				hashedPassword: users.password,
			})
			.from(users)
			.where(eq(users.email, this.data.email))
			.limit(1);
		if (!user)
			throw new HTTPError({
				httpStatus: StatusCodes.UNAUTHORIZED,
				message: ErrorMessages.EMAIL_PASSOWORD_INCORRECT,
			});
		const isPasswordCorrect = await verifyPassword(
			this.data.password,
			user.hashedPassword,
		);
		if (!isPasswordCorrect)
			throw new HTTPError({
				httpStatus: StatusCodes.UNAUTHORIZED,
				message: ErrorMessages.EMAIL_PASSOWORD_INCORRECT,
			});
		return user.id;
	}
}

export default LoginService;
