import { eq } from 'drizzle-orm';
import { StatusCodes } from 'http-status-codes';
import {
    emailValidationSchema, ErrorMessages, getStringValidationSchema, HTTPError, postgresDb, Service,
    users
} from 'totalads-shared';
import { z } from 'zod';

import SendEmailVerificationCodeService from './sendEmailVerificationCode';

export const UpdateUserDataSchema = z.object({
	name: getStringValidationSchema("name").optional().nullable(),
	email: emailValidationSchema.optional().nullable(),
});

export type UpdateUserData = z.infer<typeof UpdateUserDataSchema> & {
	userId: bigint;
};

class UpdateUserService extends Service<UpdateUserData, void> {
	async handle() {
		if (this.data.email) {
			const [user] = await postgresDb
				.select({
					id: users.id,
				})
				.from(users)
				.where(eq(users.email, this.data.email))
				.limit(1);

			if (user)
				throw new HTTPError({
					httpStatus: StatusCodes.BAD_REQUEST,
					message: ErrorMessages.ACCOUNT_ALREADY_EXISTS,
				});
		}
		await postgresDb
			.update(users)
			.set({
				...(this.data.name && {
					name: this.data.name,
				}),
				...(this.data.email && {
					email: this.data.email,
					emailVerified: false,
				}),
			})
			.where(eq(users.id, this.data.userId));

		if (this.data.email) {
			const sendEmailVerificationCodeService =
				new SendEmailVerificationCodeService({
					userId: this.data.userId,
				});
			await sendEmailVerificationCodeService.execute();
		}
	}
}

export default UpdateUserService;
