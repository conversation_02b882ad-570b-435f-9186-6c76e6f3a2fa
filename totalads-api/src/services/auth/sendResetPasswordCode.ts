import { randomBytes } from 'crypto';
import { eq } from 'drizzle-orm';
import { StatusCodes } from 'http-status-codes';
import {
    emailValidationSchema, ErrorMessages, HTTPError, InternalDiscordNotificationTypes,
    notificationSenderService, NotificationType, postgresDb, Service, userResetPasswordCodes, users
} from 'totalads-shared';
import { z } from 'zod';

export const SendResetPasswordCodeDataSchema = z.object({
	emailId: emailValidationSchema,
});

export type SendResetPasswordCodeData = z.infer<
	typeof SendResetPasswordCodeDataSchema
>;

class SendResetPasswordCodeService extends Service<
	SendResetPasswordCodeData,
	undefined
> {
	async handle(): Promise<undefined> {
		await postgresDb.transaction(async (db) => {
			const [user] = await db
				.select({
					id: users.id,
					firstName: users.firstName,
					lastName: users.lastName,
				})
				.from(users)
				.where(eq(users.email, this.data.emailId))
				.limit(1);
			if (!user)
				throw new HTTPError({
					httpStatus: StatusCodes.BAD_REQUEST,
					message: ErrorMessages.ACCOUNT_DOES_NOT_EXIST,
				});
			const resetPasswordCode = randomBytes(32).toString("hex");
			await db
				.insert(userResetPasswordCodes)
				.values({
					userId: user.id,
					resetPasswordCode,
				})
				.onConflictDoUpdate({
					target: users.id,
					set: {
						resetPasswordCode,
						sentAt: new Date(new Date().toUTCString()),
					},
				});
			await Promise.all([
				notificationSenderService.addJob("user-password-reset-email", {
					type: NotificationType.EMAIL,
					data: {
						email: this.data.emailId,
						subject: "Reset Password Code",
						html: `Please click <a href="${process.env.PORTAL_ROOT_URL}reset-password?code=${resetPasswordCode}">here</a> to reset password for your SBL account.`,
					},
				}),
				notificationSenderService.addJob("user-password-reset-email", {
					type: NotificationType.INTERNAL_DISCORD,
					data: {
						discordNotificationType:
							InternalDiscordNotificationTypes.GENERAL,
						logLevel: "info",
						logData: {
							message: "Sent password reset link to user",
							json: {
								name: user.firstName + " " + user.lastName,
								email: this.data.emailId,
							},
						},
					},
				}),
			]);
		});
	}
}

export default SendResetPasswordCodeService;
