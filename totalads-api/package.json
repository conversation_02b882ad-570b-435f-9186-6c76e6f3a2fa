{"name": "totalads-api", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start:dev:server": "tsx watch ./src/server/index.ts", "start:server": "node dist/server.js", "build:server": "tsc && node esbuild.config.js --build --entryPoint=./src/server/index.ts --outFile=./dist/server.js", "prepare": "husky"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.15.0", "@types/express": "^4.17.18", "@types/node": "^20.8.3", "@types/pg": "^8.11.11", "dotenv": "^16.4.7", "drizzle-kit": "^0.30.5", "esbuild": "^0.19.8", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "globals": "^15.12.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "prettier": "^3.4.1", "tsx": "^4.19.3", "typescript": "^5.7.2", "typescript-eslint": "^8.16.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.421.0", "@aws-sdk/s3-presigned-post": "^3.427.0", "@types/uuid": "^9.0.8", "bullmq": "^4.12.0", "drizzle-orm": "^0.40.0", "express": "^4.18.2", "http-status-codes": "^2.3.0", "pg": "^8.13.3", "postgres": "^3.3.5", "stripe": "^13.8.0", "totalads-api": "file:", "totalads-shared": "git+https://<EMAIL>/TotalAds/totalads-shared.git", "uuid": "^9.0.1", "zod": "^3.22.4"}, "lint-staged": {"**/*": ["eslint . --fix", "prettier --write .", "git add ."]}}