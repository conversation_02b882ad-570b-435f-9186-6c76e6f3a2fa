import esbuild from "esbuild";
import dotenv from "dotenv";
import process from "process";

const loadEnv = (envPath) => {
	dotenv.config({
		path: envPath,
	});
	console.log(`.env resolved from ${envPath}`);
};

const isValidId = (str) => {
	try {
		new Function(`let ${str};`);
	} catch {
		return false;
	}
	return true;
};

const args = process.argv.slice(2);
const isWatchMode = args.includes("--watch");
const isBuildMode = args.includes("--build");

const entryPoint = args.find((arg) => arg.startsWith("--entryPoint="));
if (!entryPoint) throw Error("Entry point must be specified!");

const outFile = args.find((arg) => arg.startsWith("--outFile="));
if (!outFile) throw Error("Output file must be specified!");

if (isWatchMode || isBuildMode) {
	if (isWatchMode && isBuildMode)
		throw Error("Can't run in build and watch mode both!");
	loadEnv(isWatchMode ? ".env.local" : ".env");
	const definitions = {};
	Object.keys(process.env).forEach((key) => {
		if (isValidId(key)) {
			definitions[`process.env.${key}`] = JSON.stringify(
				process.env[key],
			);
		}
	});
	const esbuildConfig = {
		entryPoints: [entryPoint.replace("--entryPoint=", "")],
		bundle: true,
		platform: "node",
		outfile: outFile.replace("--outFile=", ""),
		format: "esm",
		packages: "external",
		logLevel: "info",
		define: definitions,
	};
	if (isBuildMode) await esbuild.build(esbuildConfig);
	else {
		const esbuildContext = await esbuild.context(esbuildConfig);
		await esbuildContext.watch();
	}
	process.exit(0);
} else throw Error("Script should run in either build or watch mode!");
