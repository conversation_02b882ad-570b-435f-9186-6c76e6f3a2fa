services:
    totalads-scraper-server:
        container_name: totalads-scraper-server
        build:
            context: ../
            dockerfile: docker/server.Dockerfile
            args:
                - ENV=dev
        ports:
            - "8001:8000"
        volumes:
            - ../:/app
            - /app/node_modules/
        environment:
            - ENV=dev
        # logging:
        #     driver: awslogs
        #     options:
        #         awslogs-group: totalads-scraper
        #         awslogs-region: us-east-1
        extra_hosts:
            - "host.docker.internal:host-gateway"
