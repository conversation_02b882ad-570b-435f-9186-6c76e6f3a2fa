FROM node:20-slim AS base

SHELL ["/bin/bash", "-c"]

ARG ENV
ENV ENV=${ENV}
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD true
ENV PUPPETEER_EXECUTABLE_PATH /usr/bin/chromium

# Installing PNPM
ENV SHELL=/bin/bash
RUN corepack enable && \
    corepack prepare pnpm@latest --activate
ENV PNPM_HOME=/usr/local/bin
ENV PATH="$PNPM_HOME:$PATH"

# Install git and ssh client
RUN apt update -y && \
    apt install -y git curl chromium openssh-client

# Copy app files
COPY . /app
WORKDIR /app

FROM base AS build

# (Optional) Remove totalads-shared if it exists in package.json.
# If this package is not needed in the dependency tree at this point, run:
RUN pnpm remove totalads-shared || true

# Clean any existing modules directory and lockfile to force a fresh install.
RUN rm -rf node_modules 

# Install dependencies with a frozen lockfile to ensure consistency.
RUN pnpm install --frozen-lockfile

# Installing totalads-shared based on ENV
RUN pnpm install git+https://<EMAIL>/TotalAds/totalads-shared#main

# Check if there are eslint issues
RUN pnpm dlx eslint .

# Building server and removing devDependencies if environment is prod
RUN if [ "$ENV" == "prod" ]; then \
      pnpm run build:server; \
      pnpm prune --prod; \
  fi

FROM base

# Copy built app
COPY --from=build /app /app

# Starting server
EXPOSE 8000
CMD if [ "$ENV" == "prod" ]; then \
      pnpm run start:server; \
    else \
      pnpm run start:dev:server; \
    fi
