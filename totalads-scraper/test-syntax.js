/**
 * Test script to verify JavaScript syntax in extractors
 */

import puppeteer from "puppeteer";

async function testSyntax() {
	console.log("🧪 Testing JavaScript syntax in extractors...\n");

	const browser = await puppeteer.launch({ headless: true });
	const page = await browser.newPage();

	try {
		// Navigate to a simple page
		await page.goto(
			"data:text/html,<html><body><h1>Test</h1><p>Test content</p></body></html>",
		);

		console.log("📝 Testing about-data-extractor syntax...");

		// Test the basic structure without full extraction
		const aboutTest = await page.evaluate(`
            (function() {
                // Helper function to clean text
                function cleanText(text) {
                    if (!text) return '';
                    return text.replace(/\\\\s+/g, ' ').trim();
                }
                
                // Test founding info regex
                var pageText = 'Founded in 2020, our company started with a vision';
                var yearPattern = /(founded|established|started|since)\\\\s+(in\\\\s+)?(\\\\d{4})/gi;
                var matches = pageText.match(yearPattern);
                
                return {
                    success: true,
                    cleanTextWorks: cleanText('  test  ') === 'test',
                    regexWorks: matches && matches.length > 0
                };
            })()
        `);

		console.log("✅ About extractor syntax test:", aboutTest);

		console.log("👥 Testing team-data-extractor syntax...");

		// Test team extractor syntax
		const teamTest = await page.evaluate(`
            (function() {
                // Helper function to clean text
                function cleanText(text) {
                    if (!text) return '';
                    return text.replace(/\\\\s+/g, ' ').trim();
                }
                
                // Helper function to extract email from text
                function extractEmail(text) {
                    if (!text) return null;
                    var emailMatch = text.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}/);
                    return emailMatch ? emailMatch[0] : null;
                }
                
                // Helper function to extract phone from text
                function extractPhone(text) {
                    if (!text) return null;
                    var phoneMatch = text.match(/[+]?[0-9\\\\s\\\\-\\\\(\\\\)]{10,}/);
                    return phoneMatch ? phoneMatch[0].trim() : null;
                }
                
                return {
                    success: true,
                    cleanTextWorks: cleanText('  test  ') === 'test',
                    emailWorks: extractEmail('<EMAIL>') === '<EMAIL>',
                    phoneWorks: extractPhone('************') !== null
                };
            })()
        `);

		console.log("✅ Team extractor syntax test:", teamTest);

		if (aboutTest.success && teamTest.success) {
			console.log(
				"\n🎉 All syntax tests passed! The extractors should work correctly now.",
			);
		} else {
			console.log(
				"\n❌ Some syntax tests failed. Please check the code.",
			);
		}
	} catch (error) {
		console.error("❌ Syntax test failed:", error.message);
	} finally {
		await browser.close();
	}
}

// Run the test
testSyntax();
