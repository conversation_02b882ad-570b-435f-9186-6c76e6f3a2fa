/**
 * Performance monitoring utility for the scraper
 */

import logger from './logger';

export interface PerformanceMetrics {
  operationName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  memoryUsage?: NodeJS.MemoryUsage;
  additionalData?: Record<string, any>;
}

export interface PerformanceStats {
  totalOperations: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  totalDuration: number;
  operationsPerSecond: number;
  memoryStats?: {
    averageHeapUsed: number;
    maxHeapUsed: number;
    averageExternal: number;
  };
}

/**
 * Performance monitor class for tracking scraper performance
 */
export class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics[]> = new Map();
  private activeOperations: Map<string, PerformanceMetrics> = new Map();

  /**
   * Start tracking an operation
   * @param operationName Name of the operation
   * @param operationId Unique ID for this operation instance
   * @returns Operation ID for stopping the timer
   */
  startOperation(operationName: string, operationId?: string): string {
    const id = operationId || `${operationName}_${Date.now()}_${Math.random()}`;
    
    const metric: PerformanceMetrics = {
      operationName,
      startTime: Date.now(),
      memoryUsage: process.memoryUsage()
    };

    this.activeOperations.set(id, metric);
    return id;
  }

  /**
   * Stop tracking an operation and record the metrics
   * @param operationId Operation ID returned from startOperation
   * @param additionalData Additional data to record with the metric
   */
  stopOperation(operationId: string, additionalData?: Record<string, any>): PerformanceMetrics | null {
    const metric = this.activeOperations.get(operationId);
    if (!metric) {
      logger.warn(`No active operation found for ID: ${operationId}`);
      return null;
    }

    metric.endTime = Date.now();
    metric.duration = metric.endTime - metric.startTime;
    metric.additionalData = additionalData;

    // Store the completed metric
    if (!this.metrics.has(metric.operationName)) {
      this.metrics.set(metric.operationName, []);
    }
    this.metrics.get(metric.operationName)!.push(metric);

    // Remove from active operations
    this.activeOperations.delete(operationId);

    logger.debug(`Operation ${metric.operationName} completed in ${metric.duration}ms`);
    return metric;
  }

  /**
   * Time a function execution
   * @param operationName Name of the operation
   * @param fn Function to execute and time
   * @param additionalData Additional data to record
   * @returns Result of the function execution
   */
  async timeOperation<T>(
    operationName: string,
    fn: () => Promise<T>,
    additionalData?: Record<string, any>
  ): Promise<T> {
    const operationId = this.startOperation(operationName);
    try {
      const result = await fn();
      this.stopOperation(operationId, { ...additionalData, success: true });
      return result;
    } catch (error) {
      this.stopOperation(operationId, { 
        ...additionalData, 
        success: false, 
        error: (error as Error).message 
      });
      throw error;
    }
  }

  /**
   * Get performance statistics for an operation
   * @param operationName Name of the operation
   * @returns Performance statistics
   */
  getStats(operationName: string): PerformanceStats | null {
    const operationMetrics = this.metrics.get(operationName);
    if (!operationMetrics || operationMetrics.length === 0) {
      return null;
    }

    const durations = operationMetrics
      .filter(m => m.duration !== undefined)
      .map(m => m.duration!);

    if (durations.length === 0) {
      return null;
    }

    const totalDuration = durations.reduce((sum, duration) => sum + duration, 0);
    const averageDuration = totalDuration / durations.length;
    const minDuration = Math.min(...durations);
    const maxDuration = Math.max(...durations);

    // Calculate operations per second based on the last minute of data
    const oneMinuteAgo = Date.now() - 60000;
    const recentOperations = operationMetrics.filter(m => m.startTime > oneMinuteAgo);
    const operationsPerSecond = recentOperations.length / 60;

    // Memory statistics
    const memoryMetrics = operationMetrics
      .filter(m => m.memoryUsage !== undefined)
      .map(m => m.memoryUsage!);

    let memoryStats;
    if (memoryMetrics.length > 0) {
      const avgHeapUsed = memoryMetrics.reduce((sum, mem) => sum + mem.heapUsed, 0) / memoryMetrics.length;
      const maxHeapUsed = Math.max(...memoryMetrics.map(mem => mem.heapUsed));
      const avgExternal = memoryMetrics.reduce((sum, mem) => sum + mem.external, 0) / memoryMetrics.length;

      memoryStats = {
        averageHeapUsed: Math.round(avgHeapUsed / 1024 / 1024), // MB
        maxHeapUsed: Math.round(maxHeapUsed / 1024 / 1024), // MB
        averageExternal: Math.round(avgExternal / 1024 / 1024) // MB
      };
    }

    return {
      totalOperations: operationMetrics.length,
      averageDuration: Math.round(averageDuration),
      minDuration: Math.round(minDuration),
      maxDuration: Math.round(maxDuration),
      totalDuration: Math.round(totalDuration),
      operationsPerSecond: Math.round(operationsPerSecond * 100) / 100,
      memoryStats
    };
  }

  /**
   * Get all performance statistics
   * @returns Map of operation names to their statistics
   */
  getAllStats(): Map<string, PerformanceStats> {
    const allStats = new Map<string, PerformanceStats>();
    
    for (const operationName of this.metrics.keys()) {
      const stats = this.getStats(operationName);
      if (stats) {
        allStats.set(operationName, stats);
      }
    }

    return allStats;
  }

  /**
   * Log performance summary
   */
  logPerformanceSummary(): void {
    const allStats = this.getAllStats();
    
    if (allStats.size === 0) {
      logger.info("No performance data available");
      return;
    }

    logger.info("=== PERFORMANCE SUMMARY ===");
    
    for (const [operationName, stats] of allStats) {
      logger.info(`${operationName}:`, {
        operations: stats.totalOperations,
        avgDuration: `${stats.averageDuration}ms`,
        minDuration: `${stats.minDuration}ms`,
        maxDuration: `${stats.maxDuration}ms`,
        opsPerSec: stats.operationsPerSecond,
        memory: stats.memoryStats ? `${stats.memoryStats.averageHeapUsed}MB avg heap` : 'N/A'
      });
    }
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.clear();
    this.activeOperations.clear();
    logger.info("Performance metrics cleared");
  }

  /**
   * Get current memory usage
   * @returns Current memory usage in MB
   */
  getCurrentMemoryUsage(): { heapUsed: number; heapTotal: number; external: number; rss: number } {
    const usage = process.memoryUsage();
    return {
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
      external: Math.round(usage.external / 1024 / 1024),
      rss: Math.round(usage.rss / 1024 / 1024)
    };
  }

  /**
   * Monitor system resources
   */
  logSystemResources(): void {
    const memory = this.getCurrentMemoryUsage();
    const uptime = Math.round(process.uptime());
    
    logger.info("System Resources:", {
      uptime: `${uptime}s`,
      memory: `${memory.heapUsed}/${memory.heapTotal}MB heap, ${memory.rss}MB RSS`,
      activeOperations: this.activeOperations.size
    });
  }

  /**
   * Start periodic performance logging
   * @param intervalMs Interval in milliseconds (default: 60000 = 1 minute)
   */
  startPeriodicLogging(intervalMs: number = 60000): NodeJS.Timeout {
    return setInterval(() => {
      this.logSystemResources();
      this.logPerformanceSummary();
    }, intervalMs);
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();
export default performanceMonitor;
