/**
 * Rate limiter for controlling request frequency to prevent overwhelming websites
 */

import logger from './logger';
import { createRateLimitError } from './error-handler';

export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number; // Time window in milliseconds
  delayMs: number; // Delay between requests
  burstLimit?: number; // Allow burst of requests
}

export const DEFAULT_RATE_LIMIT: RateLimitConfig = {
  maxRequests: 10,
  windowMs: 60000, // 1 minute
  delayMs: 1000, // 1 second between requests
  burstLimit: 3
};

export const LARGE_WEBSITE_RATE_LIMIT: RateLimitConfig = {
  maxRequests: 3,
  windowMs: 60000, // 1 minute
  delayMs: 5000, // 5 seconds between requests
  burstLimit: 1
};

export const AGGRESSIVE_RATE_LIMIT: RateLimitConfig = {
  maxRequests: 1,
  windowMs: 30000, // 30 seconds
  delayMs: 10000, // 10 seconds between requests
  burstLimit: 1
};

/**
 * Token bucket rate limiter implementation
 */
export class TokenBucketRateLimiter {
  private tokens: number;
  private lastRefill: number;
  private requestTimes: number[] = [];

  constructor(private config: RateLimitConfig) {
    this.tokens = config.burstLimit || config.maxRequests;
    this.lastRefill = Date.now();
  }

  /**
   * Refill tokens based on time elapsed
   */
  private refillTokens(): void {
    const now = Date.now();
    const timePassed = now - this.lastRefill;
    
    // Calculate how many tokens to add based on time passed
    const tokensToAdd = Math.floor(timePassed / this.config.delayMs);
    
    if (tokensToAdd > 0) {
      this.tokens = Math.min(
        this.config.burstLimit || this.config.maxRequests,
        this.tokens + tokensToAdd
      );
      this.lastRefill = now;
    }
  }

  /**
   * Clean old request times outside the window
   */
  private cleanOldRequests(): void {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    this.requestTimes = this.requestTimes.filter(time => time > windowStart);
  }

  /**
   * Check if request is allowed
   */
  canMakeRequest(): boolean {
    this.refillTokens();
    this.cleanOldRequests();

    // Check window-based limit
    if (this.requestTimes.length >= this.config.maxRequests) {
      return false;
    }

    // Check token bucket
    return this.tokens > 0;
  }

  /**
   * Consume a token for a request
   */
  consumeToken(): void {
    if (!this.canMakeRequest()) {
      throw createRateLimitError('TokenBucketRateLimiter');
    }

    this.tokens--;
    this.requestTimes.push(Date.now());
  }

  /**
   * Get time until next request is allowed
   */
  getTimeUntilNextRequest(): number {
    this.refillTokens();
    this.cleanOldRequests();

    if (this.canMakeRequest()) {
      return 0;
    }

    // If window is full, wait until oldest request expires
    if (this.requestTimes.length >= this.config.maxRequests) {
      const oldestRequest = Math.min(...this.requestTimes);
      const timeUntilExpiry = (oldestRequest + this.config.windowMs) - Date.now();
      return Math.max(0, timeUntilExpiry);
    }

    // If no tokens, wait for next refill
    return this.config.delayMs;
  }

  /**
   * Wait until next request is allowed
   */
  async waitForNextRequest(): Promise<void> {
    const waitTime = this.getTimeUntilNextRequest();
    if (waitTime > 0) {
      logger.info(`Rate limiter waiting ${waitTime}ms before next request`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  /**
   * Get current status
   */
  getStatus(): {
    tokens: number;
    requestsInWindow: number;
    timeUntilNextRequest: number;
  } {
    this.refillTokens();
    this.cleanOldRequests();
    
    return {
      tokens: this.tokens,
      requestsInWindow: this.requestTimes.length,
      timeUntilNextRequest: this.getTimeUntilNextRequest()
    };
  }
}

/**
 * Domain-specific rate limiter
 */
export class DomainRateLimiter {
  private limiters = new Map<string, TokenBucketRateLimiter>();
  private domainConfigs = new Map<string, RateLimitConfig>();

  constructor() {
    // Pre-configure known large websites
    this.setDomainConfig('semrush.com', AGGRESSIVE_RATE_LIMIT);
    this.setDomainConfig('ahrefs.com', AGGRESSIVE_RATE_LIMIT);
    this.setDomainConfig('moz.com', LARGE_WEBSITE_RATE_LIMIT);
    this.setDomainConfig('hubspot.com', LARGE_WEBSITE_RATE_LIMIT);
    this.setDomainConfig('salesforce.com', LARGE_WEBSITE_RATE_LIMIT);
  }

  /**
   * Set rate limit configuration for a specific domain
   */
  setDomainConfig(domain: string, config: RateLimitConfig): void {
    this.domainConfigs.set(domain, config);
    // Remove existing limiter to force recreation with new config
    this.limiters.delete(domain);
  }

  /**
   * Get domain from URL
   */
  private getDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return 'unknown';
    }
  }

  /**
   * Get rate limiter for domain
   */
  private getLimiterForDomain(domain: string): TokenBucketRateLimiter {
    if (!this.limiters.has(domain)) {
      const config = this.domainConfigs.get(domain) || DEFAULT_RATE_LIMIT;
      this.limiters.set(domain, new TokenBucketRateLimiter(config));
    }
    return this.limiters.get(domain)!;
  }

  /**
   * Check if request to URL is allowed
   */
  canMakeRequest(url: string): boolean {
    const domain = this.getDomain(url);
    const limiter = this.getLimiterForDomain(domain);
    return limiter.canMakeRequest();
  }

  /**
   * Consume token for URL request
   */
  async consumeToken(url: string): Promise<void> {
    const domain = this.getDomain(url);
    const limiter = this.getLimiterForDomain(domain);
    
    // Wait if necessary
    await limiter.waitForNextRequest();
    
    // Consume token
    limiter.consumeToken();
    
    logger.debug(`Rate limiter: consumed token for ${domain}`, {
      domain,
      status: limiter.getStatus()
    });
  }

  /**
   * Get status for all domains
   */
  getAllStatus(): Record<string, any> {
    const status: Record<string, any> = {};
    
    for (const [domain, limiter] of this.limiters) {
      status[domain] = limiter.getStatus();
    }
    
    return status;
  }

  /**
   * Reset rate limiter for domain
   */
  resetDomain(domain: string): void {
    this.limiters.delete(domain);
    logger.info(`Rate limiter reset for domain: ${domain}`);
  }

  /**
   * Reset all rate limiters
   */
  resetAll(): void {
    this.limiters.clear();
    logger.info('All rate limiters reset');
  }
}

/**
 * Global domain rate limiter instance
 */
export const globalRateLimiter = new DomainRateLimiter();

/**
 * Adaptive rate limiter that adjusts based on errors
 */
export class AdaptiveRateLimiter extends DomainRateLimiter {
  private errorCounts = new Map<string, number>();
  private lastErrorTime = new Map<string, number>();

  /**
   * Record an error for a domain
   */
  recordError(url: string, error: Error): void {
    const domain = this.getDomain(url);
    const currentCount = this.errorCounts.get(domain) || 0;
    
    this.errorCounts.set(domain, currentCount + 1);
    this.lastErrorTime.set(domain, Date.now());
    
    // Adjust rate limit based on error count
    this.adjustRateLimit(domain, currentCount + 1);
    
    logger.warn(`Recorded error for ${domain}, total errors: ${currentCount + 1}`, {
      domain,
      error: error.message
    });
  }

  /**
   * Record successful request for a domain
   */
  recordSuccess(url: string): void {
    const domain = this.getDomain(url);
    const currentCount = this.errorCounts.get(domain) || 0;
    
    if (currentCount > 0) {
      // Gradually reduce error count on success
      this.errorCounts.set(domain, Math.max(0, currentCount - 1));
      
      // Adjust rate limit back towards normal
      this.adjustRateLimit(domain, currentCount - 1);
    }
  }

  /**
   * Adjust rate limit based on error count
   */
  private adjustRateLimit(domain: string, errorCount: number): void {
    let config: RateLimitConfig;
    
    if (errorCount >= 5) {
      // Very aggressive limiting
      config = {
        maxRequests: 1,
        windowMs: 120000, // 2 minutes
        delayMs: 30000, // 30 seconds
        burstLimit: 1
      };
    } else if (errorCount >= 3) {
      // Aggressive limiting
      config = AGGRESSIVE_RATE_LIMIT;
    } else if (errorCount >= 1) {
      // Large website limiting
      config = LARGE_WEBSITE_RATE_LIMIT;
    } else {
      // Default limiting
      config = DEFAULT_RATE_LIMIT;
    }
    
    this.setDomainConfig(domain, config);
    
    logger.info(`Adjusted rate limit for ${domain}`, {
      domain,
      errorCount,
      config
    });
  }

  private getDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return 'unknown';
    }
  }
}

/**
 * Global adaptive rate limiter instance
 */
export const adaptiveRateLimiter = new AdaptiveRateLimiter();
