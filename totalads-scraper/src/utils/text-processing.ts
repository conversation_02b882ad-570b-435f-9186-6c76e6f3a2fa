import * as cheerio from "cheerio";
import { compile } from "html-to-text";
import { Page } from "puppeteer";
import { table as getTextTable } from "table";

import { Table } from "../../types/scrapper";
import { ELEMENTS_TO_REMOVE_FROM_HTML } from "../config/constants";

export async function convertHTMLToText(page: Page): Promise<string> {
	console.log("Fetching raw HTML");
	const rawHTML = await page.content();

	console.log("Removing unnecessary elements from HTML string");
	const cleanedHTML = removeElementsFromHTMLString(
		rawHTML,
		ELEMENTS_TO_REMOVE_FROM_HTML,
	);

	console.log("Converting cleaned HTML to text");
	const text = compile({ wordwrap: null })(cleanedHTML);
	console.log("Converted cleaned HTML to text");

	return text;
}

function removeElementsFromHTMLString(
	html: string,
	selectors: string[],
): string {
	const $ = cheerio.load(html);

	selectors.forEach((sel) => {
		$(sel).remove();
	});

	// $.html() returns the serialized HTML of the root.
	return $.root().html() || "";
}

export function replaceTablePlaceholdersWithAsciiTables(
	tables: Table[],
	text: string,
): string {
	console.log("Replacing table placeholders with ascii tables in the text");
	let finalText = text;
	tables?.forEach((table, index) => {
		try {
			const { spanningCells, tableData, tableCaption, tableHeaders } =
				table;

			// Skip empty tables
			if (!tableData || tableData.length === 0) {
				finalText = finalText?.replace(`{table-${index}}`, "");
				console.log(`Table ${index} was empty, removed placeholder`);
				return;
			}

			// Find the maximum number of cells in any row
			let maxCells = 0;
			for (const row of tableData) {
				if (row && row.length > maxCells) {
					maxCells = row.length;
				}
			}

			// If no cells found, remove the placeholder
			if (maxCells === 0) {
				finalText = finalText?.replace(`{table-${index}}`, "");
				console.log(`Table ${index} had no cells, removed placeholder`);
				return;
			}

			// Create a normalized table with consistent cell counts
			const normalizedTable = [];

			// Add headers as first row if available
			if (tableHeaders && tableHeaders.length > 0) {
				const headerRow = [...tableHeaders];
				// Pad with empty cells if needed
				while (headerRow.length < maxCells) {
					headerRow.push("");
				}
				normalizedTable.push(headerRow);
			}

			// Add data rows
			for (const row of tableData) {
				if (!row) continue; // Skip null/undefined rows

				const normalizedRow = [...row];
				// Pad with empty cells if needed
				while (normalizedRow.length < maxCells) {
					normalizedRow.push("");
				}
				normalizedTable.push(normalizedRow);
			}

			// If we couldn't create a valid table, remove the placeholder
			if (normalizedTable.length === 0) {
				finalText = finalText?.replace(`{table-${index}}`, "");
				console.log(
					`Table ${index} couldn't be normalized, removed placeholder`,
				);
				return;
			}

			// Filter spanning cells to only include valid ones
			const validSpanningCells = spanningCells.filter((cell) => {
				return (
					cell &&
					typeof cell.row === "number" &&
					typeof cell.col === "number" &&
					cell.row < normalizedTable.length &&
					cell.col < maxCells
				);
			});

			// Generate the ASCII table
			const tableText = getTextTable(normalizedTable, {
				spanningCells: validSpanningCells,
				singleLine: true,
				border: {
					topBody: `─`,
					topJoin: `┬`,
					topLeft: `┌`,
					topRight: `┐`,
					bottomBody: `─`,
					bottomJoin: `┴`,
					bottomLeft: `└`,
					bottomRight: `┘`,
					bodyLeft: `│`,
					bodyRight: `│`,
					bodyJoin: `│`,
					joinBody: `─`,
					joinLeft: `├`,
					joinRight: `┤`,
					joinJoin: `┼`,
				},
			});

			// Add caption if available
			let tableOutput = "";
			if (tableCaption) {
				tableOutput += `Table: ${tableCaption}\n`;
			}
			tableOutput += tableText;

			// Replace the placeholder with the ASCII table
			finalText = finalText?.replace(
				`{table-${index}}`,
				`\n${tableOutput}\n`,
			);
		} catch (error) {
			// If there's an error, just remove the placeholder
			finalText = finalText?.replace(`{table-${index}}`, "");
			console.error(`Error formatting table ${index}:`, error);
		}
	});
	console.log(
		"Replaced all table placeholders with ascii tables in the text",
	);
	return finalText;
}
