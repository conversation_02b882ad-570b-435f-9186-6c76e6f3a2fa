import type { ScrapeResult, CompanyInfo, ContactDetails } from '../../../types/scrapper';
import webPageScraper from './index';
import logger from '../logger';
import { AboutInfo } from '../../../types/scrapper';

/**
 * This integration module provides compatibility between the new modular scraper
 * and the existing API. It ensures that the refactored scraper produces output
 * compatible with the existing ScrapeResult interface.
 */
class ScraperIntegration {
  private isInitialized = false;

  /**
   * Initialize the scraper
   */
  async initScraper() {
    if (!this.isInitialized) {
      logger.info("Initializing scraper integration");
      await webPageScraper.initScraper();
      this.isInitialized = true;
      logger.info("Scraper integration initialized successfully");
    }
  }

  /**
   * Scrape a URL and format the result to match the existing API
   * @param url URL to scrape
   * @returns ScrapeResult compatible with existing API
   */
  async scrape(url: string): Promise<ScrapeResult> {
    logger.info(`Scraping URL through integration: ${url}`);
    
    // Make sure the scraper is initialized
    if (!this.isInitialized) {
      await this.initScraper();
    }
    
    // Call the new scraper
    const result = await webPageScraper.scrape(url);
    
    // Transform to match existing API
    const scrapeResult: ScrapeResult = {
      title: result.title || null,
      desc: result.desc || null,
      nestedLinks: result.nestedLinks || [],
      text: result.text || '',
      contactDetails: result.contactDetails || {} as ContactDetails,
      // For now, we're returning an empty object for aboutData
      // In a production setting, this would call the about-info extractor
      aboutData: {} as CompanyInfo
    };
    
    logger.info(`Scraping completed for: ${url}`);
    return scrapeResult;
  }
}

// Export a singleton instance
export const scraperIntegration = new ScraperIntegration();
export default scraperIntegration;
