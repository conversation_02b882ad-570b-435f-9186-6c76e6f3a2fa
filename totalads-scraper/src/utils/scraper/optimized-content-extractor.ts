import { Page } from 'puppeteer';
import { compile } from 'html-to-text';
import logger from '../logger';
import { handleError } from '../error-handler';

/**
 * Interface for page metadata
 */
export interface PageMetadata {
  title: string | null;
  desc: string | null;
}

/**
 * Interface for optimized extraction result
 */
export interface OptimizedExtractionResult {
  metadata: PageMetadata;
  links: string[];
  text: string;
  rawHtml?: string;
}

/**
 * Optimized content extractor with single-pass extraction
 */
export class OptimizedContentExtractor {
  private htmlToTextConverter: (html: string) => string;

  constructor() {
    // Pre-compile the HTML to text converter for better performance
    this.htmlToTextConverter = compile({
      wordwrap: 130,
      selectors: [
        { selector: 'a', options: { hideLinkHrefIfSameAsText: true } },
        { selector: 'img', format: 'skip' },
        { selector: 'script', format: 'skip' },
        { selector: 'style', format: 'skip' },
        { selector: 'nav', format: 'skip' },
        { selector: 'header', format: 'skip' },
        { selector: 'footer', format: 'skip' },
        { selector: 'table', options: { uppercaseHeaderCells: false } }
      ]
    });
    logger.info("Optimized content extractor initialized");
  }

  /**
   * Extract all content in a single optimized pass
   * @param page Puppeteer page instance
   * @returns Combined extraction result
   */
  async extractAllContent(page: Page): Promise<OptimizedExtractionResult> {
    try {
      logger.info("Starting optimized single-pass content extraction");
      
      // Single page.evaluate call to extract everything at once
      const result = await page.evaluate(() => {
        // Extract metadata
        let title = document.head.querySelector("title")?.innerHTML || null;
        let desc = null;
        
        document.head.querySelectorAll("meta").forEach((meta) => {
          const metaPropertyName = 
            meta.getAttribute("name") || 
            meta.getAttribute("property") || 
            "";
          
          if (["title", "og:title"].includes(metaPropertyName)) {
            title = meta.getAttribute("content");
          } else if (["description", "og:description"].includes(metaPropertyName)) {
            desc = meta.getAttribute("content");
          }
        });

        // Extract links
        const anchors = document.querySelectorAll("a");
        const links: string[] = [];
        
        anchors.forEach((anchor) => {
          if (anchor.href && anchor.href.startsWith('http')) {
            links.push(anchor.href);
          }
        });

        // Remove unnecessary elements for cleaner text extraction
        const elementsToRemove = document.querySelectorAll(
          'script, style, nav, header, footer, .advertisement, .ads, .social-share, .cookie-banner'
        );
        elementsToRemove.forEach(el => el.remove());

        // Get cleaned HTML
        const cleanedHtml = document.documentElement.outerHTML;

        return {
          metadata: { title, desc },
          links: Array.from(new Set(links)), // Remove duplicates
          rawHtml: cleanedHtml
        };
      });

      // Convert HTML to text using the pre-compiled converter
      const text = this.htmlToTextConverter(result.rawHtml);

      logger.info(`Optimized extraction completed: ${text.length} chars, ${result.links.length} links`);

      return {
        metadata: result.metadata,
        links: result.links,
        text: text,
        rawHtml: result.rawHtml
      };
    } catch (error) {
      logger.error("Error in optimized content extraction:", error);
      return {
        metadata: { title: null, desc: null },
        links: [],
        text: `Error extracting content: ${(error as Error).message}`
      };
    }
  }

  /**
   * Extract only metadata (faster for metadata-only needs)
   * @param page Puppeteer page instance
   * @returns Page metadata
   */
  async getTitleAndDesc(page: Page): Promise<PageMetadata> {
    try {
      logger.info("Extracting title and description metadata");
      
      const metadata = await page.evaluate(() => {
        let title = document.head.querySelector("title")?.innerHTML || null;
        let desc = null;
        
        document.head.querySelectorAll("meta").forEach((meta) => {
          const metaPropertyName = 
            meta.getAttribute("name") || 
            meta.getAttribute("property") || 
            "";
          
          if (["title", "og:title"].includes(metaPropertyName)) {
            title = meta.getAttribute("content");
          } else if (["description", "og:description"].includes(metaPropertyName)) {
            desc = meta.getAttribute("content");
          }
        });
        
        return { title, desc };
      });

      logger.info(`Metadata extracted: title=${!!metadata.title}, desc=${!!metadata.desc}`);
      return metadata;
    } catch (error) {
      return handleError(error as Error, 'OptimizedContentExtractor.getTitleAndDesc');
    }
  }

  /**
   * Extract only links (faster for link-only needs)
   * @param page Puppeteer page instance
   * @returns Array of URLs
   */
  async extractLinks(page: Page): Promise<string[]> {
    try {
      logger.info("Extracting links from page");
      
      const links = await page.evaluate(() => {
        const anchors = document.querySelectorAll("a");
        const hrefs: string[] = [];
        
        anchors.forEach((anchor) => {
          if (anchor.href && anchor.href.startsWith('http')) {
            hrefs.push(anchor.href);
          }
        });
        
        return Array.from(new Set(hrefs)); // Remove duplicates
      });
      
      logger.info(`Extracted ${links.length} unique links`);
      return links;
    } catch (error) {
      return handleError(error as Error, 'OptimizedContentExtractor.extractLinks');
    }
  }

  /**
   * Convert HTML to text with optimized processing
   * @param page Puppeteer page instance
   * @returns Plain text content
   */
  async convertHTMLToText(page: Page): Promise<string> {
    try {
      logger.info("Converting HTML to text");
      
      // Get cleaned HTML in a single operation
      const cleanedHtml = await page.evaluate(() => {
        // Remove unnecessary elements
        const elementsToRemove = document.querySelectorAll(
          'script, style, nav, header, footer, .advertisement, .ads, .social-share, .cookie-banner'
        );
        elementsToRemove.forEach(el => el.remove());
        
        return document.documentElement.outerHTML;
      });
      
      // Convert using pre-compiled converter
      const text = this.htmlToTextConverter(cleanedHtml);
      logger.info(`Converted ${text.length} characters of HTML to text`);
      
      return text;
    } catch (error) {
      return handleError(error as Error, 'OptimizedContentExtractor.convertHTMLToText');
    }
  }

  /**
   * Extract content with smart filtering for specific content types
   * @param page Puppeteer page instance
   * @param contentType Type of content to focus on
   * @returns Filtered content
   */
  async extractFilteredContent(
    page: Page, 
    contentType: 'about' | 'contact' | 'team' | 'all' = 'all'
  ): Promise<string> {
    try {
      logger.info(`Extracting filtered content for: ${contentType}`);
      
      const filteredHtml = await page.evaluate((type) => {
        let selectors: string[] = [];
        
        switch (type) {
          case 'about':
            selectors = [
              '[class*="about"]', '[id*="about"]', 
              'section:has(h1:contains("About")), section:has(h2:contains("About"))',
              '.company-description', '.mission', '.vision'
            ];
            break;
          case 'contact':
            selectors = [
              '[class*="contact"]', '[id*="contact"]',
              '.contact-info', '.address', '.phone', '.email'
            ];
            break;
          case 'team':
            selectors = [
              '[class*="team"]', '[id*="team"]',
              '.team-member', '.employee', '.staff', '.leadership'
            ];
            break;
          default:
            return document.documentElement.outerHTML;
        }
        
        let filteredContent = '';
        selectors.forEach(selector => {
          try {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
              if (el.textContent) {
                filteredContent += el.outerHTML + '\n';
              }
            });
          } catch (e) {
            // Continue if selector fails
          }
        });
        
        return filteredContent || document.documentElement.outerHTML;
      }, contentType);
      
      const text = this.htmlToTextConverter(filteredHtml);
      logger.info(`Extracted ${text.length} characters of filtered content`);
      
      return text;
    } catch (error) {
      return handleError(error as Error, 'OptimizedContentExtractor.extractFilteredContent');
    }
  }
}

// Export a singleton instance
export const optimizedContentExtractor = new OptimizedContentExtractor();
export default optimizedContentExtractor;
