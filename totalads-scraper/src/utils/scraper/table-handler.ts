import { Page } from 'puppeteer';
import { SpanningCellConfig, table as getTextTable } from 'table';

import { handleError } from '../error-handler';
import logger from '../logger';

/**
 * Interface for table data structure
 */
export interface Table {
	tableData: string[][];
	spanningCells: SpanningCellConfig[];
	tableCaption?: string;
	tableHeaders?: string[];
	tableId?: string;
	tableClass?: string;
	tableSummary?: string;
}

/**
 * Handles table extraction and formatting from web pages
 */
export class TableHandler {
	private tableCount = 0;

	/**
	 * Extract tables from the page and replace with placeholders
	 * @param page Puppeteer page instance
	 * @returns Array of extracted tables and the modified HTML with placeholders
	 */
	async extractTablesAndReplacePlaceholders(page: Page): Promise<Table[]> {
		try {
			logger.info("Extracting tables from page");

			// Reset table count for this extraction
			this.tableCount = 0;

			// Use template literal string to avoid __name error
			const tables = await page.evaluate(`
        (function() {
          var results = [];

          // Helper function to clean text (using plain JS, no TypeScript)
          function cleanText(text) {
            return text
              .replace(/\\r\\n|\\n|\\r/gm, " ")
              .replace(/\\s+/g, " ")
              .trim();
          }

          // Process all tables
          var allTables = document.querySelectorAll("table");
        
        for (var i = 0; i < allTables.length; i++) {
          var table = allTables[i];
          
          // Skip empty tables
          if (!table || !table.rows || table.rows.length === 0) {
            continue;
          }
          
          // Skip tables with only one cell (likely layout tables)
          if (table.rows.length === 1 && table.rows[0] && table.rows[0].cells.length <= 1) {
            continue;
          }
          
          var tableData = [];
          var spanningCells = [];
          var tableHeaders = [];
          
          // Extract table metadata
          var tableCaption;
          var captionEl = table.querySelector("caption");
          if (captionEl && captionEl.textContent) {
            tableCaption = captionEl.textContent.trim();
          }
          
          // Extract table ID, classes, summary attributes
          var tableId = table.id || undefined;
          var tableClass = table.className || undefined;
          var tableSummary = table.getAttribute("summary") || undefined;
          
          // Handle table headers
          var tHead = table.querySelector("thead");
          if (tHead) {
            var firstRow = tHead.querySelector("tr");
            if (firstRow) {
              var firstRowHeaders = firstRow.querySelectorAll("th");
              if (firstRowHeaders.length > 0) {
                for (var j = 0; j < firstRowHeaders.length; j++) {
                  var headerCell = firstRowHeaders[j];
                  var headerText = headerCell ? headerCell.textContent || "" : "";
                  tableHeaders.push(cleanText(headerText));
                }
              }
            }
          }
          
          // Check if table is too large (performance optimization)
          var rows = table.querySelectorAll("tr");
          var isLargeTable = rows.length > 50 || (rows.length > 0 && rows[0].querySelectorAll("td, th").length > 15);
          
          // Set limit for large tables
          var rowLimit = isLargeTable ? 50 : rows.length;
          var processedRows = 0;
          
          // Process rows with limits for large tables
          for (var rowIndex = 0; rowIndex < rowLimit; rowIndex++) {
            var row = rows[rowIndex];
            if (!row) continue;
            var rowData = [];
            var cells = row.querySelectorAll("td, th");
            var cellLimit = isLargeTable ? 15 : cells.length;
            
            for (var cellIndex = 0; cellIndex < Math.min(cells.length, cellLimit); cellIndex++) {
              var cell = cells[cellIndex];
              if (!cell) continue;
              
              // Handle colspan and rowspan
              var colSpanAttr = cell.getAttribute("colspan");
              var colSpan = colSpanAttr ? parseInt(colSpanAttr, 10) : 1;
              
              var rowSpanAttr = cell.getAttribute("rowspan");
              var rowSpan = rowSpanAttr ? parseInt(rowSpanAttr, 10) : 1;
              
              if (colSpan > 1 || rowSpan > 1) {
                // Create spanning cell object with plain JS
                var spanningCell = {
                  row: rowIndex,
                  col: cellIndex
                };
                
                // Only add properties when needed to avoid undefined
                if (colSpan > 1) {
                  spanningCell.colSpan = colSpan;
                }
                
                if (rowSpan > 1) {
                  spanningCell.rowSpan = rowSpan;
                }
                
                spanningCells.push(spanningCell);
              }
              
              // Extract cell content
              var cellText = cell.textContent || "";
              rowData.push(cleanText(cellText));
            }
            
            // Add note if we truncated cells for this row
            if (cells.length > cellLimit) {
              rowData.push("... " + (cells.length - cellLimit) + " more cells");
            }
            
            if (rowData.length > 0) {
              tableData.push(rowData);
            }
          }
          
          // Only add tables with actual data
          if (tableData.length > 0) {
            // Create a placeholder for this table
            var tableInfo = {
              tableData: tableData,
              spanningCells: spanningCells,
              tableId: tableId,
              tableClass: tableClass,
              tableSummary: tableSummary
            };

            // Only add these properties if they have values to avoid undefined issues
            if (tableHeaders.length > 0) {
              tableInfo.tableHeaders = tableHeaders;
            }

            if (tableCaption) {
              tableInfo.tableCaption = tableCaption;
            }

            results.push(tableInfo);

            // Replace the table with a placeholder in the DOM
            var placeholder = document.createElement("div");
            placeholder.setAttribute("data-table-placeholder", String(i));
            placeholder.textContent = "{table-" + i + "}";

            if (table.parentNode) {
              table.parentNode.replaceChild(placeholder, table);
            }
          }
        }

        return results;
      })()
      `);

			this.tableCount = tables.length;
			logger.info(`Extracted ${tables.length} tables from the page`);

			return tables;
		} catch (error) {
			logger.error(`Error extracting tables: ${error}`);
			return [];
		}
	}

	/**
	 * Format tables into an ASCII representation and replace placeholders in text
	 * @param tables Array of tables to format
	 * @param text Text with table placeholders
	 * @returns Text with tables replaced
	 */
	formatTablesToText(tables: Table[], text: string): string {
		return this.replaceTablePlaceholders(tables, text);
	}

	/**
	 * Legacy method for formatting tables into an ASCII representation
	 * @param tables Array of tables to format
	 * @param text Text with table placeholders
	 * @returns Text with tables replaced
	 */
	replaceTablePlaceholders(tables: Table[], text: string): string {
		try {
			logger.info(`Replacing ${tables.length} table placeholders`);
			let finalText = text;

			// Process each table
			for (let index = 0; index < tables.length; index++) {
				try {
					const table = tables[index] || {
						tableData: [],
						spanningCells: [],
					};
					const {
						tableData,
						spanningCells,
						tableCaption,
						tableHeaders,
					} = table;

					// Skip empty tables
					if (!tableData || tableData.length === 0) {
						finalText = finalText.replace(`{table-${index}}`, "");
						logger.info(
							`Table ${index} is empty, removed placeholder`,
						);
						continue;
					}

					// Normalize the table (ensure consistent number of columns)
					// Find the maximum number of cells in any row
					let maxCells = 0;
					for (const row of tableData) {
						if (row && row.length > maxCells) {
							maxCells = row.length;
						}
					}

					// If no cells found, remove the placeholder
					if (maxCells === 0) {
						finalText = finalText.replace(`{table-${index}}`, "");
						logger.info(
							`Table ${index} had no cells, removed placeholder`,
						);
						continue;
					}

					// Normalize the table (add empty cells where needed)
					const tableRows = tableData.map((row) => {
						if (row.length < maxCells) {
							return [
								...row,
								...Array(maxCells - row.length).fill(""),
							];
						}
						return row;
					});

					// Add headers as first row if available
					if (tableHeaders && tableHeaders.length > 0) {
						// Process headers to match the column count
						const firstRowLength =
							tableRows && tableRows[0] ? tableRows[0].length : 0;
						const processedHeaders =
							tableHeaders.length < firstRowLength
								? [
										...tableHeaders,
										...Array(
											firstRowLength -
												tableHeaders.length,
										).fill(""),
									]
								: tableHeaders;

						tableRows.unshift(processedHeaders);
					}

					// Try to generate ASCII table
					try {
						// Filter spanning cells to only include valid ones
						const validSpanningCells = spanningCells.filter(
							(cell) => {
								return (
									cell &&
									typeof cell.row === "number" &&
									typeof cell.col === "number" &&
									tableRows &&
									tableRows.length > 0 &&
									cell.row < tableRows.length &&
									tableRows[0] &&
									cell.col < tableRows[0].length
								);
							},
						);

						const asciiTable = getTextTable(tableRows, {
							spanningCells: validSpanningCells,
							header: {
								content: tableCaption || `Table ${index + 1}`,
								alignment: "center",
							},
							drawHorizontalLine: (lineIndex, rowCount) =>
								lineIndex === 0 ||
								lineIndex === 1 ||
								lineIndex === rowCount,
						});

						// Replace placeholder with ASCII table
						finalText = finalText.replace(
							`{table-${index}}`,
							`\n${asciiTable}\n`,
						);
					} catch (tableError) {
						// If ASCII table generation fails, use a simpler approach
						logger.error(
							`Error generating ASCII table ${index}: ${tableError}`,
						);

						// Create a simple table representation
						const simpleTable = tableRows
							.map((row) => row.join(" | "))
							.join("\n");
						const caption = tableCaption
							? `${tableCaption}\n`
							: `Table ${index + 1}:\n`;

						finalText = finalText.replace(
							`{table-${index}}`,
							`\n${caption}${simpleTable}\n`,
						);
					}
				} catch (error) {
					// If there's an error, just remove the placeholder
					finalText = finalText.replace(`{table-${index}}`, "");
					logger.error(`Error formatting table ${index}: ${error}`);
				}
			}

			logger.info("Table placeholder replacement completed");
			return finalText;
		} catch (error) {
			logger.error(`Error replacing table placeholders: ${error}`);
			return text;
		}
	}
}

// Export a singleton instance
export const tableHandler = new TableHandler();
export default tableHandler;
