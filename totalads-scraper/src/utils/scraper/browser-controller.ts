import { Page } from 'puppeteer';
import puppeteer from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';

import { browserArgs, browserConfig, resourceTypesToBlock } from '../../config/browser-config';
import { createTimeoutError, ErrorCodes, handleError, ScraperError } from '../error-handler';
import logger from '../logger';
import Cluster from '../puppeteer-cluster/Cluster';
import { adaptiveRateLimiter } from '../rate-limiter';
import { largeWebsiteCircuitBreaker, withLargeWebsiteRetry, withRetry } from '../retry-handler';

// Register puppeteer stealth plugin
puppeteer.use(StealthPlugin());

/**
 * Controls browser interactions and navigation
 */
export class BrowserController {
	// Make cluster protected to allow access from extending classes
	protected _cluster?: Cluster;

	/**
	 * Get the cluster instance
	 */
	get cluster(): Cluster | undefined {
		return this._cluster;
	}

	/**
	 * Initialize the puppeteer cluster for parallel processing
	 */
	async initCluster(): Promise<Cluster> {
		try {
			logger.info("Initializing Puppeteer cluster...");

			this._cluster = await Cluster.launch({
				concurrency: Cluster.CONCURRENCY_CONTEXT,
				maxConcurrency: browserConfig.maxConcurrency,
				puppeteerOptions: {
					headless: browserConfig.headless,
					executablePath: browserConfig.executablePath,
					userDataDir: browserConfig.userDataDir,
					args: browserArgs,
				},
				timeout: browserConfig.timeout,
				monitor: true,
			});

			logger.info(
				`Cluster initialized with max concurrency: ${browserConfig.maxConcurrency}`,
			);
			return this._cluster;
		} catch (error) {
			handleError(error as Error, "BrowserController.initCluster");
			throw new Error("Failed to initialize Puppeteer cluster");
		}
	}

	/**
	 * Set up request interception to block unwanted resource types
	 * @param page Puppeteer page instance
	 */
	async setupRequestInterception(page: Page) {
		try {
			logger.info("Setting up request interception");
			await page.setRequestInterception(true);

			page.on("request", (req) => {
				if (resourceTypesToBlock.includes(req.resourceType())) {
					req.abort();
				} else {
					req.continue();
				}
			});
		} catch (error) {
			handleError(
				error as Error,
				"BrowserController.setupRequestInterception",
			);
		}
	}

	/**
	 * Navigate to a URL with proper error handling
	 * @param url URL to navigate to
	 * @param options Navigation options
	 */
	async navigateTo(
		url: string,
		options = {
			waitUntil: ["load", "domcontentloaded", "networkidle2"] as any,
			timeout: browserConfig.timeout,
		},
	): Promise<Page> {
		try {
			if (!this._cluster) {
				await this.initCluster();
			}

			if (!this._cluster) {
				throw new Error("Failed to initialize cluster");
			}

			logger.info(`Navigating to: ${url}`);
			// Use cluster.execute to run tasks with a page
			return await this._cluster.execute(url, async ({ page }) => {
				await page.goto(url, options);
				logger.info(`Navigation to ${url} successful`);
				return page;
			});
		} catch (error) {
			handleError(error as Error, "BrowserController.navigateTo");
			throw new Error(
				`Failed to navigate to ${url}: ${(error as Error).message}`,
			);
		}
	}

	/**
	 * Execute a function with a page for a given URL with enhanced error handling and rate limiting
	 * @param url - The URL to navigate to
	 * @param pageFunction - Function to execute with the page
	 * @param options - Navigation options
	 * @returns Promise with the result of pageFunction
	 */
	async executeWithPage<T>(
		url: string,
		pageFunction: (page: Page) => Promise<T>,
		options = {
			waitUntil: ["domcontentloaded"] as any, // Faster loading for large websites
			timeout: browserConfig.timeout,
		},
	): Promise<T> {
		// Check if this is a large website and use circuit breaker
		const isLargeWebsite = this.isLargeWebsite(url);

		if (isLargeWebsite) {
			return await largeWebsiteCircuitBreaker.execute(
				() =>
					this.executeWithPageInternal(
						url,
						pageFunction,
						options,
						true,
					),
				`large-website-${url}`,
			);
		} else {
			return await this.executeWithPageInternal(
				url,
				pageFunction,
				options,
				false,
			);
		}
	}

	/**
	 * Internal method for executing with page
	 */
	private async executeWithPageInternal<T>(
		url: string,
		pageFunction: (page: Page) => Promise<T>,
		options: any,
		isLargeWebsite: boolean,
	): Promise<T> {
		try {
			// Apply rate limiting before making request
			await adaptiveRateLimiter.consumeToken(url);

			if (!this._cluster) {
				await this.initCluster();
			}

			if (!this._cluster) {
				throw new Error("Failed to initialize cluster");
			}

			logger.info(
				`Executing task for URL: ${url} (large website: ${isLargeWebsite})`,
			);

			// Choose retry strategy based on website type
			const retryOperation = async (): Promise<T> => {
				return await this._cluster!.execute(url, async ({ page }) => {
					try {
						// Set resource blocking for better performance
						await page.setRequestInterception(true);
						page.on("request", (request) => {
							const resourceType = request.resourceType();
							if (resourceTypesToBlock.includes(resourceType)) {
								request.abort();
							} else {
								request.continue();
							}
						});

						// Enhanced navigation with timeout handling
						const enhancedOptions = {
							...options,
							timeout: isLargeWebsite ? 45000 : options.timeout, // Longer timeout for large websites
						};

						await page.goto(url, enhancedOptions);
						logger.info(`Navigation to ${url} successful`);

						// Execute the provided function with the page
						const result = await pageFunction(page);

						// Record success for adaptive rate limiting
						adaptiveRateLimiter.recordSuccess(url);

						return result;
					} catch (error) {
						// Record error for adaptive rate limiting
						adaptiveRateLimiter.recordError(url, error as Error);
						throw error;
					}
				});
			};

			// Use appropriate retry strategy
			if (isLargeWebsite) {
				return await withLargeWebsiteRetry(
					retryOperation,
					`large-website-scrape-${url}`,
				);
			} else {
				return await withRetry(
					retryOperation,
					undefined,
					`scrape-${url}`,
				);
			}
		} catch (error) {
			const scraperError =
				error instanceof ScraperError
					? error
					: handleError(
							error as Error,
							"BrowserController.executeWithPage",
						);

			// Log detailed error information
			logger.error(`Failed to execute task for ${url}:`, {
				error: scraperError.message,
				code: scraperError.code,
				retryable: scraperError.retryable,
				statusCode: scraperError.statusCode,
				isLargeWebsite,
			});

			throw scraperError;
		}
	}

	/**
	 * Check if URL belongs to a large website that needs special handling
	 */
	private isLargeWebsite(url: string): boolean {
		try {
			const domain = new URL(url).hostname.replace("www.", "");
			const largeWebsiteDomains = [
				"semrush.com",
				"ahrefs.com",
				"moz.com",
				"hubspot.com",
				"salesforce.com",
				"linkedin.com",
				"facebook.com",
				"google.com",
				"amazon.com",
				"microsoft.com",
			];

			return largeWebsiteDomains.some((largeDomain) =>
				domain.includes(largeDomain),
			);
		} catch {
			return false;
		}
	}

	/**
	 * Get the current browser cluster
	 * @returns The puppeteer cluster instance
	 */
	getCluster() {
		if (!this.cluster) {
			throw new Error(
				"Browser cluster not initialized. Call initBrowser() first.",
			);
		}
		return this.cluster;
	}

	/**
	 * Close the browser cluster
	 */
	async closeBrowser() {
		if (this.cluster) {
			logger.info("Closing browser cluster");
			await this.cluster.close();
			logger.info("Browser cluster closed successfully");
		}
	}
}

// Export a singleton instance
export const browserController = new BrowserController();
export default browserController;
