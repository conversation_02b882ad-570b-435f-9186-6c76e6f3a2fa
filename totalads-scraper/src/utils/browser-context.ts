/* eslint-disable @typescript-eslint/no-unsafe-function-type */
/**
 * Utility functions for working with browser context in Puppeteer
 * These functions help avoid issues with TypeScript features not available
 * in the browser context, like the __name variable.
 */

/**
 * Creates a safe browser context function that can be used with page.evaluate
 * This helps avoid "__name is not defined" errors by ensuring we use plain
 * JavaScript functions in the browser context
 *
 * @param fn The function to convert to browser-safe context
 * @returns A version of the function safe for page.evaluate
 */
export const createBrowserContextFunction = (fn: Function): Function => {
	// Convert function to string to ensure it's proper JavaScript in browser context
	const fnString = fn.toString();
	const fnBody = fnString
		.replace(/^[^{]*{\s*/, "") // Remove function declaration
		.replace(/\s*}[^}]*$/, ""); // Remove closing brace

	return new Function("return function() { " + fnBody + " }")();
};

/**
 * Creates a parameterized browser context function that accepts arguments
 *
 * @param fn The function to convert to browser-safe context
 * @returns A version of the function that accepts parameters for page.evaluate
 */
export const createParameterizedBrowserFunction = (fn: Function): string => {
	const fnString = fn.toString();
	// Return the function as a string that can be used in eval
	return fnString.replace(/^[^(]*\(/, "function(");
};

/**
 * Helper function to ensure any objects passed to the browser context
 * are properly serialized and don't contain TypeScript-specific features
 *
 * @param obj The object to prepare for browser context
 * @returns A browser-safe version of the object
 */
export const prepareBrowserContextData = <T>(obj: T): T => {
	return JSON.parse(JSON.stringify(obj));
};
