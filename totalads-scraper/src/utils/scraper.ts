// import { compile } from 'html-to-text';
// import { default as normalizeURLFromPackage } from 'normalize-url';
// import { Page } from 'puppeteer';
// import puppeteer from 'puppeteer-extra';
// import StealthPlugin from 'puppeteer-extra-plugin-stealth';
// import { SpanningCellConfig, table as getTextTable } from 'table';

// /* eslint-disable no-undef */
// import { ELEMENTS_TO_REMOVE_FROM_FINAL_HTML, MAX_CONCURRENCY } from '../config/constants.ts';
// import Cluster from './puppeteer-cluster/Cluster.ts';

// type Table = {
// 	tableData: string[][];
// 	spanningCells: SpanningCellConfig[];
// 	tableCaption?: string;
// 	tableHeaders?: string[];
// 	tableId?: string;
// 	tableClass?: string;
// 	tableSummary?: string;
// };

// interface ContactDetails {
// 	name?: string;
// 	email?: string[];
// 	phone?: string[];
// 	address?: string;
// 	socialLinks?: Record<string, string>;
// }

// puppeteer.use(StealthPlugin());

// export class WebPageScraper {
// 	cluster?: Cluster;

// 	async initScraper() {
// 		console.log("Initialising cluster");
// 		this.cluster = await Cluster.launch({
// 			concurrency: Cluster.CONCURRENCY_PAGE,
// 			maxConcurrency: MAX_CONCURRENCY,
// 			puppeteer,
// 			timeout: 0,
// 			puppeteerOptions: {
// 				timeout: 0,
// 				// Use the correct Chrome executable path based on the operating system
// 				executablePath:
// 					process.platform === "win32"
// 						? "C:\\Users\\<USER>\\.cache\\puppeteer\\chrome\\win64-121.0.6167.85\\chrome-win64\\chrome.exe"
// 						: "/usr/bin/chromium",
// 				headless: true,
// 				// Set a specific temp directory for Puppeteer profiles
// 				userDataDir: "./puppeteer_temp",
// 				args: [
// 					"--disable-features=site-per-process",
// 					"--disable-gpu",
// 					"--disable-dev-shm-usage",
// 					"--disable-setuid-sandbox",
// 					"--no-first-run",
// 					"--no-sandbox",
// 					"--no-zygote",
// 					"--deterministic-fetch",
// 					"--disable-features=IsolateOrigins",
// 					"--disable-site-isolation-trials",
// 					"--disable-accelerated-2d-canvas",
// 				],
// 			},
// 		});
// 		console.log("Initialized cluster successfully");
// 	}

// 	private normalizeURL(url: string) {
// 		return normalizeURLFromPackage(url, {
// 			stripHash: true,
// 			stripTextFragment: true,
// 			stripWWW: false,
// 		});
// 	}

// 	private async getTitleAndDesc(page: Page) {
// 		console.log("Getting title and desc of the page");
// 		const result = await page.evaluate(() => {
// 			let title = document.head.querySelector("title")?.innerHTML || null,
// 				desc = null;
// 			document.head.querySelectorAll("meta").forEach((meta) => {
// 				const metaPropertyName =
// 					meta.getAttribute("name") ||
// 					meta.getAttribute("property") ||
// 					"";
// 				if (["title", "og:title"].includes(metaPropertyName))
// 					title = meta.getAttribute("content");
// 				else if (
// 					["description", "og:description"].includes(metaPropertyName)
// 				)
// 					desc = meta.getAttribute("content");
// 			});
// 			return {
// 				title,
// 				desc,
// 			};
// 		});
// 		console.log(`Title - ${result.title}\nDesc - ${result.desc}`);
// 		return result;
// 	}

// 	private async getTablesAndReplaceWithPlaceholders(page: Page) {
// 		console.log(
// 			"Getting all tables in the page and replacing with placeholders.",
// 		);

// 		try {
// 			// Use a simpler approach to avoid the __name error
// 			const tables = await page.evaluate(() => {
// 				const result = [];

// 				// Helper function to clean text
// 				function cleanText(text: string) {
// 					return text
// 						.replace(/\r\n|\n|\r/gm, " ")
// 						.replace(/\s+/g, " ")
// 						.trim();
// 				}

// 				// Process all tables
// 				const allTables = document.querySelectorAll("table");

// 				for (let i = 0; i < allTables.length; i++) {
// 					const table = allTables[i];

// 					// Skip empty tables
// 					if (!table?.rows || table.rows.length === 0) {
// 						continue;
// 					}

// 					// Skip tables with only one cell (likely layout tables)
// 					if (
// 						table.rows.length === 1 &&
// 						table.rows[0] &&
// 						table.rows[0].cells.length <= 1
// 					) {
// 						continue;
// 					}

// 					const tableData = [];
// 					const spanningCells = [];
// 					const tableHeaders = [];

// 					// Extract table metadata
// 					let tableCaption;
// 					const captionEl = table.querySelector("caption");
// 					if (captionEl && captionEl.textContent) {
// 						tableCaption = captionEl.textContent.trim();
// 					}

// 					const tableId = table.id || undefined;
// 					const tableClass = table.className || undefined;
// 					const tableSummary =
// 						table.getAttribute("summary") || undefined;

// 					// Extract headers from thead if present
// 					const theadRows = table.querySelectorAll("thead tr");
// 					if (theadRows.length > 0) {
// 						const headerCells =
// 							theadRows[0]?.querySelectorAll("th");
// 						if (headerCells) {
// 							for (let j = 0; j < headerCells.length; j++) {
// 								const headerText =
// 									headerCells[j]?.textContent || "";
// 								tableHeaders.push(cleanText(headerText));
// 							}
// 						}
// 					}

// 					// If no headers in thead, check first row for th elements
// 					if (tableHeaders.length === 0 && table.rows.length > 0) {
// 						const firstRow = table.rows[0];
// 						if (firstRow) {
// 							const firstRowHeaders =
// 								firstRow.querySelectorAll("th");
// 							if (firstRowHeaders.length > 0) {
// 								for (
// 									let j = 0;
// 									j < firstRowHeaders.length;
// 									j++
// 								) {
// 									const headerText =
// 										firstRowHeaders[j]?.textContent || "";
// 									tableHeaders.push(cleanText(headerText));
// 								}
// 							}
// 						}
// 					}

// 					// Process all rows
// 					const rows = table.querySelectorAll("tr");
// 					for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
// 						const row = rows[rowIndex];

// 						// Skip header row if we already processed it
// 						if (
// 							rowIndex === 0 &&
// 							tableHeaders.length > 0 &&
// 							row?.querySelector("th")
// 						) {
// 							continue;
// 						}

// 						const rowData = [];
// 						const cells = row?.querySelectorAll("td, th") || [];

// 						// Skip empty rows
// 						if (cells.length === 0) continue;

// 						for (
// 							let cellIndex = 0;
// 							cellIndex < cells.length;
// 							cellIndex++
// 						) {
// 							const cell = cells[cellIndex];

// 							if (cell) {
// 								// Handle spanning cells
// 								const colSpan = cell.getAttribute("colspan")
// 									? parseInt(
// 											cell.getAttribute("colspan") || "1",
// 											10,
// 										)
// 									: 1;
// 								const rowSpan = cell.getAttribute("rowspan")
// 									? parseInt(
// 											cell.getAttribute("rowspan") || "1",
// 											10,
// 										)
// 									: 1;

// 								if (colSpan > 1 || rowSpan > 1) {
// 									spanningCells.push({
// 										row: rowIndex,
// 										col: cellIndex,
// 										colSpan:
// 											colSpan > 1 ? colSpan : undefined,
// 										rowSpan:
// 											rowSpan > 1 ? rowSpan : undefined,
// 									});
// 								}

// 								// Extract cell content
// 								const cellText = cell.textContent || "";
// 								rowData.push(cleanText(cellText));
// 							}
// 						}

// 						// Only add non-empty rows
// 						if (rowData.some((cell) => cell.trim().length > 0)) {
// 							tableData.push(rowData);
// 						}
// 					}

// 					// Only add tables with actual data
// 					if (
// 						tableData.length > 0 &&
// 						tableData[0] &&
// 						tableData[0].length > 0
// 					) {
// 						result.push({
// 							tableData,
// 							spanningCells,
// 							tableCaption,
// 							tableHeaders:
// 								tableHeaders.length > 0
// 									? tableHeaders
// 									: undefined,
// 							tableId,
// 							tableClass,
// 							tableSummary,
// 						});

// 						// Replace table with placeholder
// 						table.innerHTML = `{table-${i}}`;
// 					}
// 				}

// 				return result;
// 			});

// 			console.log(
// 				"Got all tables and replaced with placeholders:",
// 				tables,
// 			);
// 			return tables;
// 		} catch (error) {
// 			console.error("Error extracting tables:", error);
// 			return []; // Return empty array on error
// 		}
// 	}

// 	private async getNestedLinks(page: Page) {
// 		console.log("Getting all nested links in the page");
// 		const nestedAnchorsHrefs = await page.evaluate(() => {
// 			const nestedAnchors = document.querySelectorAll("a");
// 			const nestedAnchorsHrefs: string[] = [];
// 			nestedAnchors.forEach((nestedAnchor) =>
// 				nestedAnchorsHrefs.push(nestedAnchor.href),
// 			);
// 			return nestedAnchorsHrefs;
// 		});
// 		const currentPageURL = new URL(this.normalizeURL(page.url()));
// 		const nestedLinks = new Set<string>();
// 		nestedAnchorsHrefs.forEach((href) => {
// 			let nestedAnchorHref = href;
// 			let nestedURL: URL;
// 			try {
// 				nestedURL = new URL(this.normalizeURL(href));
// 			} catch {
// 				nestedAnchorHref = `${currentPageURL.origin}${
// 					nestedAnchorHref.startsWith("/") ? "" : "/"
// 				}${nestedAnchorHref}`;
// 				try {
// 					nestedURL = new URL(this.normalizeURL(nestedAnchorHref));
// 				} catch {
// 					return;
// 				}
// 			}
// 			if (
// 				nestedURL.origin === currentPageURL.origin &&
// 				nestedURL.href !== currentPageURL.href
// 			)
// 				nestedLinks.add(nestedURL.href);
// 		});
// 		console.log(`Nested links: ${Array.from(nestedLinks)}`);
// 		return nestedLinks;
// 	}

// 	private async removeUnnecessaryElements(page: Page) {
// 		console.log("Removing unnecessary elements for the page");
// 		const removedCount = await page.evaluate(
// 			(ELEMENTS_TO_REMOVE_FROM_FINAL_HTML) => {
// 				let removedCount = 0;
// 				document
// 					.querySelectorAll(
// 						ELEMENTS_TO_REMOVE_FROM_FINAL_HTML.join(", "),
// 					)
// 					.forEach((el) => {
// 						removedCount++;
// 						el.remove();
// 					});
// 				return removedCount;
// 			},
// 			ELEMENTS_TO_REMOVE_FROM_FINAL_HTML,
// 		);
// 		console.log(
// 			`Removed ${removedCount} unnecessary elements from the page`,
// 		);
// 	}

// 	private async convertFinalHTMLToText(page: Page) {
// 		console.log("Converting final HTML to text");
// 		const finalHTML = await page.content();
// 		const text = compile({
// 			wordwrap: null,
// 		})(finalHTML);
// 		console.log("Converted final HTML to text");
// 		return text;
// 	}

// 	private replaceTablePlaceholdersWithAsciiTables(
// 		tables: Table[],
// 		text: string,
// 	) {
// 		console.log(
// 			"Replacing table placeholders with ascii tables in the text",
// 		);
// 		let finalText = text;
// 		tables?.forEach((table, index) => {
// 			try {
// 				const { spanningCells, tableData, tableCaption, tableHeaders } =
// 					table;

// 				// Skip empty tables
// 				if (!tableData || tableData.length === 0) {
// 					finalText = finalText?.replace(`{table-${index}}`, "");
// 					console.log(
// 						`Table ${index} was empty, removed placeholder`,
// 					);
// 					return;
// 				}

// 				// Find the maximum number of cells in any row
// 				let maxCells = 0;
// 				for (const row of tableData) {
// 					if (row && row.length > maxCells) {
// 						maxCells = row.length;
// 					}
// 				}

// 				// If no cells found, remove the placeholder
// 				if (maxCells === 0) {
// 					finalText = finalText?.replace(`{table-${index}}`, "");
// 					console.log(
// 						`Table ${index} had no cells, removed placeholder`,
// 					);
// 					return;
// 				}

// 				// Create a normalized table with consistent cell counts
// 				const normalizedTable = [];

// 				// Add headers as first row if available
// 				if (tableHeaders && tableHeaders.length > 0) {
// 					const headerRow = [...tableHeaders];
// 					// Pad with empty cells if needed
// 					while (headerRow.length < maxCells) {
// 						headerRow.push("");
// 					}
// 					normalizedTable.push(headerRow);
// 				}

// 				// Add data rows
// 				for (const row of tableData) {
// 					if (!row) continue; // Skip null/undefined rows

// 					const normalizedRow = [...row];
// 					// Pad with empty cells if needed
// 					while (normalizedRow.length < maxCells) {
// 						normalizedRow.push("");
// 					}
// 					normalizedTable.push(normalizedRow);
// 				}

// 				// If we couldn't create a valid table, remove the placeholder
// 				if (normalizedTable.length === 0) {
// 					finalText = finalText?.replace(`{table-${index}}`, "");
// 					console.log(
// 						`Table ${index} couldn't be normalized, removed placeholder`,
// 					);
// 					return;
// 				}

// 				// Filter spanning cells to only include valid ones
// 				const validSpanningCells = spanningCells.filter((cell) => {
// 					return (
// 						cell &&
// 						typeof cell.row === "number" &&
// 						typeof cell.col === "number" &&
// 						cell.row < normalizedTable.length &&
// 						cell.col < maxCells
// 					);
// 				});

// 				// Generate the ASCII table
// 				const tableText = getTextTable(normalizedTable, {
// 					spanningCells: validSpanningCells,
// 					singleLine: true,
// 					border: {
// 						topBody: `─`,
// 						topJoin: `┬`,
// 						topLeft: `┌`,
// 						topRight: `┐`,
// 						bottomBody: `─`,
// 						bottomJoin: `┴`,
// 						bottomLeft: `└`,
// 						bottomRight: `┘`,
// 						bodyLeft: `│`,
// 						bodyRight: `│`,
// 						bodyJoin: `│`,
// 						joinBody: `─`,
// 						joinLeft: `├`,
// 						joinRight: `┤`,
// 						joinJoin: `┼`,
// 					},
// 				});

// 				// Add caption if available
// 				let tableOutput = "";
// 				if (tableCaption) {
// 					tableOutput += `Table: ${tableCaption}\n`;
// 				}
// 				tableOutput += tableText;

// 				// Replace the placeholder with the ASCII table
// 				finalText = finalText?.replace(
// 					`{table-${index}}`,
// 					`\n${tableOutput}\n`,
// 				);
// 			} catch (error) {
// 				// If there's an error, just remove the placeholder
// 				finalText = finalText?.replace(`{table-${index}}`, "");
// 				console.error(`Error formatting table ${index}:`, error);
// 			}
// 		});
// 		console.log(
// 			"Replaced all table placeholders with ascii tables in the text",
// 		);
// 		return finalText;
// 	}

// 	// New method for extracting contact details
// 	private async extractContactDetailsFromPage(
// 		page: Page,
// 	): Promise<ContactDetails> {
// 		return await page.evaluate(() => {
// 			const contactDetails: ContactDetails = {
// 				email: [],
// 				phone: [],
// 				socialLinks: {},
// 			};

// 			// Extract company/organization name
// 			const nameSelectors = [
// 				"h1", // Often the main heading is the company name
// 				".company-name",
// 				".org-name",
// 				".site-title",
// 				'meta[property="og:site_name"]',
// 				"#logo img",
// 				".logo img",
// 			];

// 			for (const selector of nameSelectors) {
// 				let nameElement;
// 				try {
// 					if (selector === 'meta[property="og:site_name"]') {
// 						nameElement = document.querySelector(selector);
// 						if (nameElement) {
// 							contactDetails.name =
// 								nameElement.getAttribute("content") ??
// 								undefined;
// 							break;
// 						}
// 					} else if (selector.includes("img")) {
// 						nameElement = document.querySelector(selector);
// 						if (nameElement) {
// 							contactDetails.name = nameElement
// 								.getAttribute("alt")
// 								?.replace(" logo", "");
// 							break;
// 						}
// 					} else {
// 						nameElement = document.querySelector(selector);
// 						if (nameElement && nameElement.textContent?.trim()) {
// 							contactDetails.name =
// 								nameElement.textContent.trim();
// 							break;
// 						}
// 					}
// 				} catch (e) {
// 					console.log("Error extracting name:", e);
// 					// Continue if selector fails
// 				}
// 			}

// 			// Extract emails using regex
// 			const emailRegex =
// 				/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
// 			const pageText = document.body.innerText;
// 			const emailMatches = pageText.match(emailRegex) || [];

// 			contactDetails.email = Array.from(new Set(emailMatches));

// 			// Extract from mailto links
// 			document.querySelectorAll('a[href^="mailto:"]').forEach((el) => {
// 				const mailtoHref = el.getAttribute("href");
// 				if (mailtoHref) {
// 					const email = mailtoHref
// 						?.replace("mailto:", "")
// 						?.split("?")[0]
// 						?.trim();
// 					if (email && !contactDetails.email!.includes(email)) {
// 						contactDetails.email!.push(email);
// 					}
// 				}
// 			});

// 			// Extract phone numbers
// 			const phoneRegex =
// 				/(?:\+\d{1,3}[- ]?)?\(?\d{3}\)?[- ]?\d{3}[- ]?\d{4}/g;
// 			const phoneMatches = pageText.match(phoneRegex) || [];
// 			contactDetails.phone = Array.from(new Set(phoneMatches));

// 			// Additional extraction from tel: links
// 			document.querySelectorAll('a[href^="tel:"]').forEach((el) => {
// 				const telHref = el.getAttribute("href");
// 				if (telHref) {
// 					const phone = telHref.replace("tel:", "").trim();
// 					if (phone && !contactDetails.phone!.includes(phone)) {
// 						contactDetails.phone!.push(phone);
// 					}
// 				}
// 			});

// 			// Extract address
// 			// Look for address in structured data
// 			const addressSelectors = [
// 				".address",
// 				".contact-address",
// 				"address",
// 				".location",
// 				'[itemprop="address"]',
// 				".vcard",
// 				".contact-info address",
// 				".footer address",
// 				".contact address",
// 			];

// 			for (const selector of addressSelectors) {
// 				try {
// 					const addressElement = document.querySelector(selector);
// 					if (addressElement && addressElement.textContent?.trim()) {
// 						contactDetails.address = addressElement.textContent
// 							.trim()
// 							.replace(/\s+/g, " ") // Replace multiple spaces with single space
// 							.replace(/\n+/g, ", "); // Replace newlines with commas
// 						break;
// 					}
// 				} catch (e) {
// 					console.log("Error extracting address:", e);
// 					// Continue if selector fails
// 				}
// 			}

// 			// If address not found, try to find address-like content
// 			if (!contactDetails.address) {
// 				// Try to find address by looking for patterns in text containing postal codes or states
// 				const addressRegex =
// 					/\b(\d{1,5}\s[\w\s-]{1,20}(?:street|st|avenue|ave|road|rd|highway|hwy|square|sq|trail|trl|drive|dr|court|ct|park|parkway|pkwy|circle|cir|boulevard|blvd|way|lane|ln)\s?(?:[,]\s?)?(?:[a-zA-Z]{2,30}\s)?(?:[\d]{5})?)(?=[,\n]|$)/i;

// 				// Find all paragraphs that may contain address info
// 				document.querySelectorAll("p").forEach((para) => {
// 					if (contactDetails.address) return;

// 					const text = para.textContent || "";
// 					const match = text.match(addressRegex);
// 					if (match) {
// 						contactDetails.address = match[0].trim();
// 					}
// 				});
// 			}

// 			// Extract social media links
// 			const socialPlatforms = {
// 				"facebook.com": "facebook",
// 				"twitter.com": "twitter",
// 				"x.com": "twitter",
// 				"linkedin.com": "linkedin",
// 				"instagram.com": "instagram",
// 				"youtube.com": "youtube",
// 				"pinterest.com": "pinterest",
// 				"tiktok.com": "tiktok",
// 			};

// 			document.querySelectorAll("a[href]").forEach((link) => {
// 				const href = link.getAttribute("href");
// 				if (!href) return;

// 				for (const [domain, platform] of Object.entries(
// 					socialPlatforms,
// 				)) {
// 					if (href.includes(domain)) {
// 						contactDetails.socialLinks![platform] = href;
// 						break;
// 					}
// 				}
// 			});

// 			return contactDetails;
// 		});
// 	}

// 	private async findContactPages(
// 		page: Page,
// 		baseUrl: string,
// 	): Promise<string[]> {
// 		console.log("Finding contact/about pages");

// 		return await page.evaluate((baseUrl) => {
// 			const contactPageKeywords = [
// 				"contact",
// 				"about",
// 				"about-us",
// 				"about us",
// 				"get in touch",
// 				"reach us",
// 				"connect",
// 				"location",
// 				"find us",
// 				"meet",
// 				"team",
// 				"support",
// 				"help",
// 				"contact-us",
// 				"contact us",
// 			];
// 			const contactLinks: string[] = [];

// 			// Find all links
// 			document.querySelectorAll("a").forEach((link) => {
// 				const href = link.getAttribute("href");
// 				const text = link.textContent?.toLowerCase().trim() || "";

// 				if (!href) return;

// 				// Check if link text or href contains contact-related keywords
// 				const isContactLink = contactPageKeywords.some(
// 					(keyword) =>
// 						text.includes(keyword) ||
// 						href.toLowerCase().includes(keyword),
// 				);

// 				if (isContactLink) {
// 					let fullUrl = href;

// 					// Handle relative URLs
// 					if (href.startsWith("/")) {
// 						try {
// 							const url = new URL(baseUrl);
// 							fullUrl = `${url.origin}${href}`;
// 						} catch (e) {
// 							console.log(`Error parsing URL: ${baseUrl}`, e);
// 							return;
// 						}
// 					} else if (!href.startsWith("http")) {
// 						fullUrl = `${baseUrl}${href.startsWith("/") ? "" : "/"}${href}`;
// 					}

// 					// Only add URLs from the same domain
// 					try {
// 						const linkUrl = new URL(fullUrl);
// 						const baseUrlObj = new URL(baseUrl);

// 						if (linkUrl.hostname === baseUrlObj.hostname) {
// 							contactLinks.push(fullUrl);
// 						}
// 					} catch (e) {
// 						console.log(`Error parsing URL: ${fullUrl}`, e);
// 						// Skip invalid URLs
// 					}
// 				}
// 			});

// 			return contactLinks;
// 		}, baseUrl);
// 	}

// 	private isContactDetailsComplete(details: ContactDetails): boolean {
// 		// Consider details complete if we have at least one email and phone number
// 		const hasEmail = details.email && details.email.length > 0;
// 		const hasPhone = details.phone && details.phone.length > 0;

// 		// Return true if we have both email and phone
// 		return Boolean(hasEmail && hasPhone);
// 	}

// 	private mergeContactDetails(
// 		existing: ContactDetails,
// 		additional: ContactDetails,
// 	): ContactDetails {
// 		const merged: ContactDetails = { ...existing };

// 		// Merge emails
// 		if (additional.email && additional.email.length > 0) {
// 			merged.email = Array.from(
// 				new Set([...(existing.email || []), ...additional.email]),
// 			);
// 		}

// 		// Merge phones
// 		if (additional.phone && additional.phone.length > 0) {
// 			merged.phone = Array.from(
// 				new Set([...(existing.phone || []), ...additional.phone]),
// 			);
// 		}

// 		// Set address if it doesn't exist
// 		if (!existing.address && additional.address) {
// 			merged.address = additional.address;
// 		}

// 		// Set name if it doesn't exist
// 		if (!existing.name && additional.name) {
// 			merged.name = additional.name;
// 		}

// 		// Merge social links
// 		merged.socialLinks = {
// 			...(existing.socialLinks || {}),
// 			...(additional.socialLinks || {}),
// 		};

// 		return merged;
// 	}

// 	private async extractAboutAndTeamInfo(page: Page): Promise<{
// 		companyDescription?: string;
// 		foundingInfo?: string;
// 		missionStatement?: string;
// 		teamMembers?: Array<{
// 			name: string;
// 			title?: string;
// 			bio?: string;
// 			imageUrl?: string;
// 			contact?: {
// 				email?: string;
// 				phone?: string;
// 				linkedin?: string;
// 			};
// 		}>;
// 		companyValues?: string[];
// 		awards?: string[];
// 		industries?: string[];
// 	}> {
// 		console.log("Extracting about page and team information");

// 		return await page.evaluate(() => {
// 			const aboutInfo = {
// 				companyDescription: undefined as string | undefined,
// 				foundingInfo: undefined as string | undefined,
// 				missionStatement: undefined as string | undefined,
// 				teamMembers: undefined as
// 					| Array<{
// 							name: string;
// 							title?: string;
// 							bio?: string;
// 							imageUrl?: string;
// 							contact?: {
// 								email?: string;
// 								phone?: string;
// 								linkedin?: string;
// 							};
// 					  }>
// 					| undefined,
// 				companyValues: undefined as string[] | undefined,
// 				awards: undefined as string[] | undefined,
// 				industries: undefined as string[] | undefined,
// 			};

// 			// Extract company description
// 			const descriptionSelectors = [
// 				".about-description",
// 				".company-description",
// 				".about-content p",
// 				".about-section p",
// 				".about-us p",
// 				"#about p",
// 				"section.about p",
// 				'[data-section="about"] p',
// 			];

// 			for (const selector of descriptionSelectors) {
// 				try {
// 					const elements = document.querySelectorAll(selector);
// 					if (elements && elements.length > 0) {
// 						// Get first three paragraphs if available
// 						const paragraphs = Array.from(elements)
// 							.slice(0, 3)
// 							.map((el) => el.textContent?.trim())
// 							.filter((text) => text && text.length > 50) // Only substantial paragraphs
// 							.join("\n\n");

// 						if (paragraphs && paragraphs.length > 100) {
// 							aboutInfo.companyDescription = paragraphs;
// 							break;
// 						}
// 					}
// 				} catch (e) {
// 					console.log("Error extracting company description:", e);
// 					// Continue if selector fails
// 				}
// 			}

// 			// Extract founding info
// 			const foundingTextRegex = /found(?:ed|ing)(?:\s+in)?\s+(\d{4})/i;
// 			const bodyText = document.body.innerText;
// 			const foundingMatch = bodyText.match(foundingTextRegex);

// 			if (foundingMatch) {
// 				// Get the sentence containing the founding info
// 				const sentences = bodyText.split(/[.!?]+/);
// 				for (const sentence of sentences) {
// 					if (sentence.match(foundingTextRegex)) {
// 						aboutInfo.foundingInfo = sentence.trim();
// 						break;
// 					}
// 				}
// 			}

// 			// Extract mission statement
// 			const missionSelectors = [
// 				".mission-statement",
// 				".mission",
// 				".vision",
// 				".our-mission",
// 				"#mission",
// 				"blockquote",
// 				'h2:contains("Mission") + p',
// 				'h3:contains("Mission") + p',
// 			];

// 			for (const selector of missionSelectors) {
// 				try {
// 					let element;
// 					if (selector.includes(":contains")) {
// 						// Handle special case for heading-based selectors
// 						const parts = selector.split(":contains(");
// 						const headingType = parts[0];
// 						const textContent = parts[1]?.replace('") + p', "");

// 						// Find headings with this text
// 						const headings = Array.from(
// 							document.querySelectorAll(headingType || ""),
// 						);
// 						const matchingHeading = headings.find((h) =>
// 							h.textContent?.includes(textContent || ""),
// 						);

// 						// Get next paragraph if heading found
// 						if (matchingHeading) {
// 							element = matchingHeading.nextElementSibling;
// 						}
// 					} else {
// 						element = document.querySelector(selector);
// 					}

// 					if (element && element.textContent?.trim()) {
// 						const text = element.textContent.trim();
// 						if (text.length > 20) {
// 							aboutInfo.missionStatement = text;
// 							break;
// 						}
// 					}
// 				} catch (e) {
// 					// Continue if selector fails
// 					console.log("Error extracting mission statement:", e);
// 				}
// 			}

// 			// Extract team members
// 			const teamSelectors = [
// 				".team-member",
// 				".team-card",
// 				".member",
// 				".employee",
// 				".staff-member",
// 				".people-card",
// 				".bio-card",
// 				".team-grid > div",
// 				".leadership-team > div",
// 				".people > div",
// 			];

// 			for (const selector of teamSelectors) {
// 				try {
// 					const elements = document.querySelectorAll(selector);
// 					if (elements && elements.length > 0) {
// 						const teamMembers = Array.from(elements)
// 							.map((member) => {
// 								const teamMember: {
// 									name: string;
// 									title?: string;
// 									bio?: string;
// 									imageUrl?: string;
// 									contact?: {
// 										email?: string;
// 										phone?: string;
// 										linkedin?: string;
// 									};
// 								} = { name: "" };

// 								// Extract name
// 								const nameElement = member.querySelector(
// 									"h2, h3, h4, .name, .member-name, strong",
// 								);
// 								if (
// 									nameElement &&
// 									nameElement.textContent?.trim()
// 								) {
// 									teamMember.name =
// 										nameElement.textContent.trim();
// 								}

// 								// Extract title/position
// 								const titleElement = member.querySelector(
// 									".title, .position, .job-title, .role, em",
// 								);
// 								if (
// 									titleElement &&
// 									titleElement.textContent?.trim()
// 								) {
// 									teamMember.title =
// 										titleElement.textContent.trim();
// 								}

// 								// Extract bio
// 								const bioElement = member.querySelector(
// 									"p, .bio, .description, .about",
// 								);
// 								if (
// 									bioElement &&
// 									bioElement.textContent?.trim()
// 								) {
// 									teamMember.bio =
// 										bioElement.textContent.trim();
// 								}

// 								// Extract image
// 								const imageElement =
// 									member.querySelector("img");
// 								if (
// 									imageElement &&
// 									imageElement.getAttribute("src")
// 								) {
// 									const imgSrc =
// 										imageElement.getAttribute("src");
// 									// Handle relative URLs
// 									if (imgSrc?.startsWith("/")) {
// 										try {
// 											teamMember.imageUrl = new URL(
// 												imgSrc,
// 												window.location.origin,
// 											).href;
// 										} catch (e) {
// 											console.log(
// 												"Error parsing image URL:",
// 												e,
// 											);
// 											teamMember.imageUrl = imgSrc;
// 										}
// 									} else {
// 										teamMember.imageUrl =
// 											imgSrc || undefined;
// 									}
// 								}

// 								// Extract contact info
// 								const contact: {
// 									email?: string;
// 									phone?: string;
// 									linkedin?: string;
// 								} = {};

// 								// Email
// 								const emailElement =
// 									member.querySelector('a[href^="mailto:"]');
// 								if (emailElement) {
// 									const mailtoHref =
// 										emailElement.getAttribute("href");
// 									if (mailtoHref) {
// 										contact.email = mailtoHref
// 											?.replace("mailto:", "")
// 											?.split("?")[0]
// 											?.trim();
// 									}
// 								}

// 								// Phone
// 								const phoneElement =
// 									member.querySelector('a[href^="tel:"]');
// 								if (phoneElement) {
// 									const telHref =
// 										phoneElement.getAttribute("href");
// 									if (telHref) {
// 										contact.phone = telHref
// 											.replace("tel:", "")
// 											.trim();
// 									}
// 								}

// 								// LinkedIn
// 								const linkedinElement = member.querySelector(
// 									'a[href*="linkedin.com"]',
// 								);
// 								if (linkedinElement) {
// 									contact.linkedin =
// 										linkedinElement.getAttribute("href") ||
// 										undefined;
// 								}

// 								if (Object.keys(contact).length > 0) {
// 									teamMember.contact = contact;
// 								}

// 								return teamMember;
// 							})
// 							.filter((member) => member.name); // Only include members with names

// 						if (teamMembers.length > 0) {
// 							aboutInfo.teamMembers = teamMembers;
// 							break;
// 						}
// 					}
// 				} catch (e) {
// 					console.log("Error extracting team members:", e);
// 					// Continue if selector fails
// 				}
// 			}

// 			// Extract company values
// 			const valuesSelectors = [
// 				".values",
// 				".core-values",
// 				".our-values",
// 				".value-item",
// 				"#values",
// 				".principles",
// 				'h2:contains("Values") + ul li',
// 				'h3:contains("Values") + ul li',
// 			];

// 			for (const selector of valuesSelectors) {
// 				try {
// 					let elements;
// 					if (selector.includes(":contains")) {
// 						// Handle special case for heading-based selectors
// 						const parts = selector.split(":contains(");
// 						const headingType = parts[0];
// 						const textContent = parts[1]?.split('") + ')[0];
// 						const targetElement = parts[1]?.split('") + ')[1];

// 						// Find headings with this text
// 						const headings = Array.from(
// 							document.querySelectorAll(headingType || ""),
// 						);
// 						const matchingHeading = headings.find((h) =>
// 							h.textContent?.includes(textContent || ""),
// 						);

// 						// Get list items if heading found
// 						if (
// 							matchingHeading &&
// 							matchingHeading.nextElementSibling
// 						) {
// 							const nextElement =
// 								matchingHeading.nextElementSibling;
// 							if (
// 								nextElement.tagName.toLowerCase() ===
// 								targetElement
// 							) {
// 								elements = nextElement.querySelectorAll("li");
// 							}
// 						}
// 					} else {
// 						elements = document.querySelectorAll(selector);
// 					}

// 					if (elements && elements.length > 0) {
// 						const values = Array.from(elements)
// 							.map((el) => el.textContent?.trim())
// 							.filter((text) => text) as string[];

// 						if (values.length > 0) {
// 							aboutInfo.companyValues = values;
// 							break;
// 						}
// 					}
// 				} catch (e) {
// 					console.log("Error extracting company values:", e);
// 					// Continue if selector fails
// 				}
// 			}

// 			// Extract awards or recognition
// 			const awardsSelectors = [
// 				".awards",
// 				".recognition",
// 				".achievements",
// 				".award-item",
// 				"#awards",
// 				".honors",
// 				'h2:contains("Award") + ul li',
// 				'h3:contains("Award") + ul li',
// 			];

// 			for (const selector of awardsSelectors) {
// 				try {
// 					let elements;
// 					if (selector.includes(":contains")) {
// 						// Similar handling as values section
// 						const parts = selector.split(":contains(");
// 						const headingType = parts[0];
// 						const textContent = parts[1]?.split('") + ')[0];
// 						const targetElement = parts[1]?.split('") + ')[1];

// 						const headings = Array.from(
// 							document.querySelectorAll(headingType || ""),
// 						);
// 						const matchingHeading = headings.find((h) =>
// 							h.textContent?.includes(textContent || ""),
// 						);

// 						if (
// 							matchingHeading &&
// 							matchingHeading.nextElementSibling
// 						) {
// 							const nextElement =
// 								matchingHeading.nextElementSibling;
// 							if (
// 								nextElement.tagName.toLowerCase() ===
// 								targetElement
// 							) {
// 								elements = nextElement.querySelectorAll("li");
// 							}
// 						}
// 					} else {
// 						elements = document.querySelectorAll(selector);
// 					}

// 					if (elements && elements.length > 0) {
// 						const awards = Array.from(elements)
// 							.map((el) => el.textContent?.trim())
// 							.filter((text) => text) as string[];

// 						if (awards.length > 0) {
// 							aboutInfo.awards = awards;
// 							break;
// 						}
// 					}
// 				} catch (e) {
// 					console.log("Error extracting awards:", e);
// 					// Continue if selector fails
// 				}
// 			}

// 			// Extract industries or sectors
// 			const industrySelectors = [
// 				".industries",
// 				".sectors",
// 				".specialties",
// 				".expertise",
// 				"#industries",
// 				".markets",
// 				'h2:contains("Industries") + ul li',
// 				'h3:contains("Industries") + ul li',
// 			];

// 			for (const selector of industrySelectors) {
// 				try {
// 					let elements;
// 					if (selector.includes(":contains")) {
// 						// Similar handling as previous sections
// 						const parts = selector.split(":contains(");
// 						const headingType = parts[0];
// 						const textContent = parts[1]?.split('") + ')[0];
// 						const targetElement = parts[1]?.split('") + ')[1];

// 						const headings = Array.from(
// 							document.querySelectorAll(headingType || ""),
// 						);
// 						const matchingHeading = headings.find((h) =>
// 							h.textContent?.includes(textContent || ""),
// 						);

// 						if (
// 							matchingHeading &&
// 							matchingHeading.nextElementSibling
// 						) {
// 							const nextElement =
// 								matchingHeading.nextElementSibling;
// 							if (
// 								nextElement.tagName.toLowerCase() ===
// 								targetElement
// 							) {
// 								elements = nextElement.querySelectorAll("li");
// 							}
// 						}
// 					} else {
// 						elements = document.querySelectorAll(selector);
// 					}

// 					if (elements && elements.length > 0) {
// 						const industries = Array.from(elements)
// 							.map((el) => el.textContent?.trim())
// 							.filter((text) => text) as string[];

// 						if (industries.length > 0) {
// 							aboutInfo.industries = industries;
// 							break;
// 						}
// 					}
// 				} catch (e) {
// 					console.log("Error extracting industries:", e);
// 					// Continue if selector fails
// 				}
// 			}

// 			return aboutInfo;
// 		});
// 	}

// 	private async scrapeTask({ page, url }: { page: Page; url: string }) {
// 		console.log("Attach request listener to abort unwanted requests.");
// 		await page.setRequestInterception(true);

// 		page.on("request", (req) => {
// 			if (
// 				["image", "media", "font", "texttrack"].includes(
// 					req.resourceType(),
// 				)
// 			)
// 				req.abort();
// 			else req.continue();
// 		});

// 		console.log(`Navigating to page: ${url}`);
// 		await page.goto(url, {
// 			waitUntil: ["load", "domcontentloaded", "networkidle2"],
// 			timeout: 0,
// 		});
// 		console.log(`Navigated to page: ${url} successfully`);

// 		const { title, desc } = await this.getTitleAndDesc(page);
// 		const tables = await this.getTablesAndReplaceWithPlaceholders(page);
// 		const nestedLinks = Array.from(await this.getNestedLinks(page));
// 		await this.removeUnnecessaryElements(page);
// 		let text = await this.convertFinalHTMLToText(page);
// 		text = this.replaceTablePlaceholdersWithAsciiTables(tables, text);
// 		// Extract contact details from the current page
// 		console.log("Extracting contact details from the current page");
// 		const contactDetails = await this.extractContactDetailsFromPage(page);
// 		console.log(`Scraped page: ${url} successfully`);
// 		return {
// 			title,
// 			desc,
// 			nestedLinks,
// 			text,
// 			contactDetails,
// 		};
// 	}

// 	async scrape(url: string) {
// 		if (!this.cluster)
// 			throw Error("initScraper should be called before calling scrape.");
// 		const output = await this.cluster.execute(
// 			this.normalizeURL(url),
// 			async ({ page, data }) => {
// 				const basicOutput = await this.scrapeTask({ page, url: data });

// 				// If contact details are incomplete, try to find more info on other pages
// 				if (
// 					!this.isContactDetailsComplete(basicOutput.contactDetails)
// 				) {
// 					console.log(
// 						"Contact details incomplete, looking for contact/about pages",
// 					);
// 					const contactLinks = await this.findContactPages(
// 						page,
// 						data,
// 					);

// 					// Visit each potential contact page until we find complete details
// 					for (const link of contactLinks) {
// 						if (
// 							this.isContactDetailsComplete(
// 								basicOutput.contactDetails,
// 							)
// 						)
// 							break;

// 						console.log(
// 							`Navigating to potential contact page: ${link}`,
// 						);
// 						try {
// 							await page.goto(link, {
// 								waitUntil: [
// 									"load",
// 									"domcontentloaded",
// 									"networkidle2",
// 								],
// 								timeout: 30000,
// 							});

// 							const pageDetails =
// 								await this.extractContactDetailsFromPage(page);
// 							basicOutput.contactDetails =
// 								this.mergeContactDetails(
// 									basicOutput.contactDetails,
// 									pageDetails,
// 								);
// 						} catch (error) {
// 							console.error(
// 								`Error navigating to ${link}:`,
// 								error,
// 							);
// 						}
// 					}
// 				}

// 				const aboutData = await this.extractAboutAndTeamInfo(page);

// 				const enhancedOutput = {
// 					...basicOutput,
// 					aboutData,
// 				};

// 				return enhancedOutput;
// 			},
// 		);
// 		return output;
// 	}
// }

// const webPageScraper = new WebPageScraper();
// await webPageScraper.initScraper();

// export default webPageScraper;
