/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-function-type */
import logger from './logger';

export class ScraperError extends Error {
	public retryable: boolean;
	public statusCode: number;
	public retryAfter?: number; // seconds to wait before retry

	constructor(
		message: string,
		public code: string = "SCRAPER_ERROR",
		public originalError?: Error,
		retryable: boolean = false,
		statusCode: number = 500,
		retryAfter?: number,
	) {
		super(message);
		this.name = "ScraperError";
		this.retryable = retryable;
		this.statusCode = statusCode;
		this.retryAfter = retryAfter;

		// Maintain proper stack trace
		if (Error.captureStackTrace) {
			Error.captureStackTrace(this, ScraperError);
		}
	}
}

export enum ErrorCodes {
	TIMEOUT = "TIMEOUT_ERROR",
	NAVIGATION_FAILED = "NAVIGATION_FAILED",
	RATE_LIMITED = "RATE_LIMITED",
	BLOCKED = "BLOCKED_BY_WEBSITE",
	NETWORK_ERROR = "NETWORK_ERROR",
	MEMORY_ERROR = "MEMORY_ERROR",
	FRAME_DETACHED = "FRAME_DETACHED",
	CAPTCHA_DETECTED = "CAPTCHA_DETECTED",
	LARGE_WEBSITE = "LARGE_WEBSITE_ERROR",
	UNKNOWN = "UNKNOWN_ERROR",
}

export function categorizeError(error: Error): {
	code: ErrorCodes;
	retryable: boolean;
	statusCode: number;
	retryAfter?: number;
} {
	const message = error.message.toLowerCase();

	// Timeout errors - common with large websites
	if (message.includes("timeout") || message.includes("navigation timeout")) {
		return {
			code: ErrorCodes.TIMEOUT,
			retryable: true,
			statusCode: 408,
			retryAfter: 30, // Wait 30 seconds before retry
		};
	}

	// Navigation errors
	if (
		message.includes("navigation failed") ||
		message.includes("net::err_")
	) {
		return {
			code: ErrorCodes.NAVIGATION_FAILED,
			retryable: true,
			statusCode: 502,
			retryAfter: 15,
		};
	}

	// Rate limiting - common with large websites like Semrush
	if (
		message.includes("rate limit") ||
		message.includes("too many requests") ||
		message.includes("429")
	) {
		return {
			code: ErrorCodes.RATE_LIMITED,
			retryable: true,
			statusCode: 429,
			retryAfter: 60, // Wait 1 minute for rate limits
		};
	}

	// Blocked by website (anti-bot measures)
	if (
		message.includes("blocked") ||
		message.includes("forbidden") ||
		message.includes("403")
	) {
		return {
			code: ErrorCodes.BLOCKED,
			retryable: false,
			statusCode: 403,
		};
	}

	// Network errors
	if (
		message.includes("network") ||
		message.includes("connection") ||
		message.includes("dns")
	) {
		return {
			code: ErrorCodes.NETWORK_ERROR,
			retryable: true,
			statusCode: 502,
			retryAfter: 10,
		};
	}

	// Memory errors
	if (message.includes("memory") || message.includes("heap")) {
		return {
			code: ErrorCodes.MEMORY_ERROR,
			retryable: false,
			statusCode: 507,
		};
	}

	// Frame detached - common with complex websites
	if (message.includes("frame") && message.includes("detached")) {
		return {
			code: ErrorCodes.FRAME_DETACHED,
			retryable: true,
			statusCode: 502,
			retryAfter: 5,
		};
	}

	// CAPTCHA detection
	if (message.includes("captcha") || message.includes("recaptcha")) {
		return {
			code: ErrorCodes.CAPTCHA_DETECTED,
			retryable: false,
			statusCode: 403,
		};
	}

	// Large website specific errors
	if (
		message.includes("semrush") ||
		message.includes("large") ||
		message.includes("complex")
	) {
		return {
			code: ErrorCodes.LARGE_WEBSITE,
			retryable: true,
			statusCode: 502,
			retryAfter: 45,
		};
	}

	return {
		code: ErrorCodes.UNKNOWN,
		retryable: false,
		statusCode: 500,
	};
}

export function handleError(err: Error, context = ""): never {
	const { code, retryable, statusCode, retryAfter } = categorizeError(err);

	const error =
		err instanceof ScraperError
			? err
			: new ScraperError(
					`Error in ${context}: ${err.message}`,
					code,
					err,
					retryable,
					statusCode,
					retryAfter,
				);

	logger.error({
		message: error.message,
		code: error.code,
		retryable: error.retryable,
		statusCode: error.statusCode,
		retryAfter: error.retryAfter,
		stack: error.stack,
		context,
	});

	throw error;
}

export function createTimeoutError(
	timeout: number,
	context?: string,
): ScraperError {
	return new ScraperError(
		`Operation timed out after ${timeout}ms`,
		ErrorCodes.TIMEOUT,
		undefined,
		true,
		408,
		30,
	);
}

export function createRateLimitError(
	context?: string,
	retryAfter: number = 60,
): ScraperError {
	return new ScraperError(
		"Rate limit exceeded",
		ErrorCodes.RATE_LIMITED,
		undefined,
		true,
		429,
		retryAfter,
	);
}

export function catchAndLog(fn: Function, context = "") {
	return async (...args: any[]) => {
		try {
			return await fn(...args);
		} catch (error) {
			handleError(error as Error, context);
		}
	};
}
