/**
 * Retry mechanism for handling failed operations with exponential backoff
 */

import logger from './logger';
import { ScraperError, ErrorCodes } from './error-handler';

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  exponentialBase: number;
  retryableErrors: ErrorCodes[];
}

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 30000, // 30 seconds
  exponentialBase: 2,
  retryableErrors: [
    ErrorCodes.TIMEOUT,
    ErrorCodes.NAVIGATION_FAILED,
    ErrorCodes.RATE_LIMITED,
    ErrorCodes.NETWORK_ERROR,
    ErrorCodes.FRAME_DETACHED,
    ErrorCodes.LARGE_WEBSITE
  ]
};

export const LARGE_WEBSITE_RETRY_CONFIG: RetryConfig = {
  maxRetries: 5,
  baseDelay: 5000, // 5 seconds
  maxDelay: 60000, // 1 minute
  exponentialBase: 1.5,
  retryableErrors: [
    ErrorCodes.TIMEOUT,
    ErrorCodes.NAVIGATION_FAILED,
    ErrorCodes.RATE_LIMITED,
    ErrorCodes.NETWORK_ERROR,
    ErrorCodes.FRAME_DETACHED,
    ErrorCodes.LARGE_WEBSITE
  ]
};

/**
 * Sleep for a specified number of milliseconds
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Calculate delay with exponential backoff and jitter
 */
export function calculateDelay(
  attempt: number, 
  config: RetryConfig, 
  retryAfter?: number
): number {
  // If the error specifies a retry-after time, use that
  if (retryAfter) {
    return Math.min(retryAfter * 1000, config.maxDelay);
  }

  // Calculate exponential backoff
  const exponentialDelay = config.baseDelay * Math.pow(config.exponentialBase, attempt - 1);
  
  // Add jitter (random factor between 0.5 and 1.5)
  const jitter = 0.5 + Math.random();
  const delayWithJitter = exponentialDelay * jitter;
  
  // Cap at maximum delay
  return Math.min(delayWithJitter, config.maxDelay);
}

/**
 * Check if an error is retryable based on configuration
 */
export function isRetryableError(error: Error | ScraperError, config: RetryConfig): boolean {
  if (error instanceof ScraperError) {
    return error.retryable && config.retryableErrors.includes(error.code as ErrorCodes);
  }
  
  // For non-ScraperError, check common retryable patterns
  const message = error.message.toLowerCase();
  return message.includes('timeout') || 
         message.includes('network') || 
         message.includes('connection') ||
         message.includes('frame') && message.includes('detached');
}

/**
 * Retry a function with exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG,
  operationName: string = 'operation'
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
    try {
      logger.info(`Attempting ${operationName} (attempt ${attempt}/${config.maxRetries + 1})`);
      const result = await operation();
      
      if (attempt > 1) {
        logger.info(`${operationName} succeeded on attempt ${attempt}`);
      }
      
      return result;
    } catch (error) {
      lastError = error as Error;
      
      // If this is the last attempt, throw the error
      if (attempt > config.maxRetries) {
        logger.error(`${operationName} failed after ${config.maxRetries + 1} attempts:`, {
          error: lastError.message,
          attempts: attempt
        });
        throw lastError;
      }
      
      // Check if error is retryable
      if (!isRetryableError(lastError, config)) {
        logger.error(`${operationName} failed with non-retryable error:`, {
          error: lastError.message,
          attempt
        });
        throw lastError;
      }
      
      // Calculate delay
      const retryAfter = lastError instanceof ScraperError ? lastError.retryAfter : undefined;
      const delay = calculateDelay(attempt, config, retryAfter);
      
      logger.warn(`${operationName} failed on attempt ${attempt}, retrying in ${delay}ms:`, {
        error: lastError.message,
        nextAttempt: attempt + 1,
        delay
      });
      
      // Wait before retrying
      await sleep(delay);
    }
  }
  
  // This should never be reached, but TypeScript requires it
  throw lastError!;
}

/**
 * Retry specifically for large websites with enhanced configuration
 */
export async function withLargeWebsiteRetry<T>(
  operation: () => Promise<T>,
  operationName: string = 'large website operation'
): Promise<T> {
  return withRetry(operation, LARGE_WEBSITE_RETRY_CONFIG, operationName);
}

/**
 * Rate limit aware retry - respects rate limit headers
 */
export async function withRateLimitRetry<T>(
  operation: () => Promise<T>,
  operationName: string = 'rate limited operation'
): Promise<T> {
  const rateLimitConfig: RetryConfig = {
    ...DEFAULT_RETRY_CONFIG,
    maxRetries: 2, // Fewer retries for rate limits
    baseDelay: 60000, // Start with 1 minute
    maxDelay: 300000, // Max 5 minutes
    retryableErrors: [ErrorCodes.RATE_LIMITED, ErrorCodes.TIMEOUT]
  };
  
  return withRetry(operation, rateLimitConfig, operationName);
}

/**
 * Circuit breaker pattern for repeated failures
 */
export class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(
    private failureThreshold: number = 5,
    private recoveryTimeout: number = 60000 // 1 minute
  ) {}
  
  async execute<T>(operation: () => Promise<T>, operationName: string = 'operation'): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.state = 'HALF_OPEN';
        logger.info(`Circuit breaker for ${operationName} moving to HALF_OPEN state`);
      } else {
        throw new ScraperError(
          `Circuit breaker is OPEN for ${operationName}`,
          ErrorCodes.BLOCKED,
          undefined,
          false,
          503
        );
      }
    }
    
    try {
      const result = await operation();
      
      if (this.state === 'HALF_OPEN') {
        this.state = 'CLOSED';
        this.failures = 0;
        logger.info(`Circuit breaker for ${operationName} recovered to CLOSED state`);
      }
      
      return result;
    } catch (error) {
      this.failures++;
      this.lastFailureTime = Date.now();
      
      if (this.failures >= this.failureThreshold) {
        this.state = 'OPEN';
        logger.error(`Circuit breaker for ${operationName} opened after ${this.failures} failures`);
      }
      
      throw error;
    }
  }
  
  getState(): string {
    return this.state;
  }
  
  reset(): void {
    this.state = 'CLOSED';
    this.failures = 0;
    this.lastFailureTime = 0;
  }
}

// Global circuit breaker for large websites
export const largeWebsiteCircuitBreaker = new CircuitBreaker(3, 120000); // 2 minutes recovery
