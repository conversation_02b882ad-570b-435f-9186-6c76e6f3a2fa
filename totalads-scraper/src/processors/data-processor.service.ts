/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Processor service for handling data processing tasks
 */

import { CompanyInfo, ContactDetails } from '../models/scraper.model';
import { handleError } from '../utils/error-handler';
import logger from '../utils/logger';
import { DataProcessor } from '../utils/scraper/data-processor';

/**
 * Enhanced data processor service that builds on the base utility
 * and adds more advanced processing capabilities
 */
export class DataProcessorService {
	private dataProcessor: DataProcessor;

	constructor() {
		this.dataProcessor = new DataProcessor();
		logger.info("Data processor service initialized");
	}

	/**
	 * Normalize URLs to a standard format
	 * @param url URL to normalize
	 * @returns Normalized URL
	 */
	normalizeURL(url: string): string {
		try {
			return this.dataProcessor.normalizeURL(url);
		} catch (error) {
			logger.warn(
				`Failed to normalize URL: ${url}, ${(error as Error).message}`,
			);
			return url;
		}
	}

	/**
	 * Extract contact details from text
	 * @param text Text to extract contact details from
	 * @returns Contact details
	 */
	extractContactDetails(
		text: string,
		additionalText?: string,
	): ContactDetails {
		try {
			// Extract contact details from text
			const contactDetails: ContactDetails = {
				email: this.extractEmails(text),
				phone: this.extractPhoneNumbers(text),
				// Add other contact detail extractions as needed
			};

			// If additional text is provided, extract from that too and merge
			if (additionalText) {
				const additionalDetails: ContactDetails = {
					email: this.extractEmails(additionalText),
					phone: this.extractPhoneNumbers(additionalText),
					// Add other contact detail extractions as needed
				};
				return this.mergeContactInfo(contactDetails, additionalDetails);
			}

			return contactDetails;
		} catch (error) {
			handleError(
				error as Error,
				"DataProcessorService.extractContactDetails",
			);
			return {};
		}
	}

	/**
	 * Extract email addresses from text
	 * @param text Text to extract from
	 * @returns Array of email addresses
	 */
	private extractEmails(text: string): string[] {
		if (!text) return [];

		const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
		const matches = text.match(emailRegex);
		return matches ? [...new Set(matches)] : [];
	}

	/**
	 * Extract phone numbers from text
	 * @param text Text to extract from
	 * @returns Array of phone numbers
	 */
	private extractPhoneNumbers(text: string): string[] {
		if (!text) return [];

		// Basic phone regex - can be improved for more specific formats
		const phoneRegex =
			/(?:\+?\d{1,3}[- ]?)?\(?\d{3}\)?[- ]?\d{3}[- ]?\d{4}/g;
		const matches = text.match(phoneRegex);
		return matches ? [...new Set(matches)] : [];
	}

	/**
	 * Merge two contact details objects
	 * @param primary Primary contact details
	 * @param secondary Secondary contact details
	 * @returns Merged contact details
	 */
	private mergeContactInfo(
		primary: ContactDetails,
		secondary: ContactDetails,
	): ContactDetails {
		const merged: ContactDetails = { ...primary };

		// Merge arrays (email, phone, etc.)
		if (secondary.email && secondary.email.length > 0) {
			merged.email = [
				...new Set([...(merged.email || []), ...secondary.email]),
			];
		}

		if (secondary.phone && secondary.phone.length > 0) {
			merged.phone = [
				...new Set([...(merged.phone || []), ...secondary.phone]),
			];
		}

		// Merge other fields as needed
		if (!merged.name && secondary.name) {
			merged.name = secondary.name;
		}

		if (!merged.address && secondary.address) {
			merged.address = secondary.address;
		}

		// Merge social links
		if (secondary.socialLinks) {
			merged.socialLinks = {
				...(merged.socialLinks || {}),
				...secondary.socialLinks,
			};
		}

		return merged;
	}

	/**
	 * Clean text by removing excessive whitespace and normalizing line breaks
	 * @param text Text to clean
	 * @returns Cleaned text
	 */
	cleanText(text: string): string {
		try {
			return this.dataProcessor.cleanText(text);
		} catch (error) {
			logger.warn(`Failed to clean text: ${(error as Error).message}`);
			return text;
		}
	}

	/**
	 * Merge company information from multiple sources
	 * @param sources Array of company information objects to merge
	 * @returns Merged company information
	 */
	mergeCompanyInfo(...sources: (CompanyInfo | undefined)[]): CompanyInfo {
		try {
			logger.debug(
				`Merging ${sources.length} company information sources`,
			);

			// Filter out undefined/empty sources
			const validSources = sources.filter(
				(source): source is CompanyInfo =>
					source !== undefined && Object.keys(source).length > 0,
			);

			if (validSources.length === 0) {
				return {};
			}

			// Start with the first source
			const merged: CompanyInfo = { ...validSources[0] };

			// Merge in additional sources
			for (let i = 1; i < validSources.length; i++) {
				const source = validSources[i];

				// TypeScript knows source is defined here because of our filter above
				// but we'll add an additional check just to be safe
				if (!source) continue;

				// Merge each property selectively
				Object.keys(source).forEach((key) => {
					const typedKey = key as keyof CompanyInfo;

					// Handle arrays (concatenate and deduplicate)
					if (Array.isArray(source[typedKey])) {
						if (!merged[typedKey]) {
							merged[typedKey] = [...source[typedKey]] as any;
						} else if (Array.isArray(merged[typedKey])) {
							// Concatenate arrays and remove duplicates
							const combined = [
								...(merged[typedKey] as any[]),
								...(source[typedKey] as any[]),
							];
							merged[typedKey] = [...new Set(combined)] as any;
						}
					}
					// Handle team members separately (merge by name)
					else if (typedKey === "teamMembers") {
						if (!merged.teamMembers) {
							merged.teamMembers = [
								...(source.teamMembers || []),
							];
						} else {
							// Create map of existing members by name
							const memberMap = new Map();
							merged.teamMembers.forEach((member) => {
								memberMap.set(
									member.name.toLowerCase(),
									member,
								);
							});

							// Add or update members from source
							(source.teamMembers || []).forEach((member) => {
								const existingMember = memberMap.get(
									member.name.toLowerCase(),
								);
								if (existingMember) {
									// Merge member details
									memberMap.set(member.name.toLowerCase(), {
										...existingMember,
										...member,
										contact: {
											...(existingMember.contact || {}),
											...(member.contact || {}),
										},
									});
								} else {
									memberMap.set(
										member.name.toLowerCase(),
										member,
									);
								}
							});

							// Convert map back to array
							merged.teamMembers = Array.from(memberMap.values());
						}
					}
					// For boolean values, use OR operation
					else if (typeof source[typedKey] === "boolean") {
						// Handle boolean values with OR operation
						const sourceValue = source[typedKey] as boolean;
						if (merged[typedKey] === undefined) {
							// Cast to any to avoid type errors when assigning boolean to possibly undefined
							(merged as any)[typedKey] = sourceValue;
						} else {
							// TypeScript knows merged[typedKey] is defined here
							(merged as any)[typedKey] = Boolean(
								(merged[typedKey] as boolean) || sourceValue,
							);
						}
					}
					// For other types, prefer non-empty values
					else if (!merged[typedKey] && source[typedKey]) {
						merged[typedKey] = source[typedKey] as string;
					}
				});
			}

			return merged;
		} catch (error) {
			handleError(
				error as Error,
				"DataProcessorService.mergeCompanyInfo",
			);
			return {};
		}
	}
}

// Export a singleton instance
export const dataProcessorService = new DataProcessorService();
export default dataProcessorService;
