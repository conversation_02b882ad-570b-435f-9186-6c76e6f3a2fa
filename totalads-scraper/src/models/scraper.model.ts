/**
 * Core models for the scraper system
 */

import { Table, ContactDetails, AboutInfo, CompanyInfo, ScrapeResult } from '../../types/scrapper';

/**
 * Browser configuration model
 */
export interface BrowserConfig {
  executablePath?: string;
  headless: boolean;
  userDataDir?: string;
  args?: string[];
  timeout: number;
  maxConcurrency: number;
}

/**
 * Browser task model for queuing scraping jobs
 */
export interface BrowserTask {
  url: string;
  taskId: string;
  options?: {
    followRedirects?: boolean;
    maxDepth?: number;
    timeout?: number;
  };
}

/**
 * Scraper result with additional metadata
 */
export interface EnhancedScrapeResult extends ScrapeResult {
  url: string;
  timestamp: Date;
  processingTime?: number;
  status: 'success' | 'partial' | 'failed';
  error?: string;
}

export {
  // Re-export types from the existing types file
  Table,
  ContactDetails,
  AboutInfo,
  CompanyInfo,
  ScrapeResult
};
