import { Router } from 'express';
// Import our new modular API routes
import apiRoutes from '../../api/routes';

/**
 * This is a compatibility wrapper that forwards requests to our new modular API
 * This allows us to maintain backward compatibility while migrating to the new architecture
 */
const scrapeRouter = Router();

// Forward all requests to our new API routes
scrapeRouter.use('/', apiRoutes);

export default scrapeRouter;
