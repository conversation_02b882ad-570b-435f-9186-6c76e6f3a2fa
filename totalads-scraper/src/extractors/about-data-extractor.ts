import { Page } from 'puppeteer';

import logger from '../utils/logger';

/**
 * Interface for company about information
 */
export interface AboutData {
	companyDescription?: string;
	foundingInfo?: string;
	missionStatement?: string;
	visionStatement?: string;
	companyValues?: string[];
	awards?: string[];
	industries?: string[];
	globalPresence?: boolean;
	officeLocations?: string[];
	certifications?: string[];
	keyPoints?: string[];
}

/**
 * Extracts company about information from a webpage
 * @param page - Puppeteer page instance
 * @returns Promise containing about data
 */
export async function extractAboutData(page: Page): Promise<AboutData> {
	logger.info("Extracting company about information");

	try {
		// Use string-based evaluation to avoid TypeScript transpilation issues
		const result = await page.evaluate(`
      (function() {
        // Define our structure in plain JavaScript
        var aboutData = {
          companyDescription: undefined,
          foundingInfo: undefined,
          missionStatement: undefined,
          visionStatement: undefined,
          companyValues: [],
          awards: [],
          industries: [],
          globalPresence: false,
          officeLocations: [],
          certifications: [],
          keyPoints: []
        };

        // Helper function to clean text
        function cleanText(text) {
          if (!text) return '';
          return text.replace(/\\\\s+/g, ' ').trim();
        }

        // Helper function to check if text contains keywords
        function containsKeywords(text, keywords) {
          if (!text) return false;
          var lowerText = text.toLowerCase();
          return keywords.some(function(keyword) {
            return lowerText.includes(keyword.toLowerCase());
          });
        }

        // Extract company description from main content areas
        function extractCompanyDescription() {
          var descriptions = [];

          // Enhanced selectors for about sections
          var aboutSelectors = [
            '[class*="about"]',
            '[id*="about"]',
            '.company-description',
            '.about-content',
            '.about-section',
            '.company-overview',
            '.overview',
            '.intro',
            '.introduction',
            '.company-info',
            '.description',
            '.summary',
            '.hero-content',
            '.main-content p:first-of-type',
            'meta[name="description"]',
            'meta[property="og:description"]',
            '.lead',
            '.subtitle',
            '.tagline'
          ];

          // Try each selector
          aboutSelectors.forEach(function(selector) {
            try {
              var elements = document.querySelectorAll(selector);
              elements.forEach(function(element) {
                if (element && element.textContent) {
                  var text = cleanText(element.textContent);
                  if (text.length > 50 && text.length < 3000) {
                    descriptions.push(text);
                  }
                }
              });
            } catch (e) {
              // Continue if selector fails
            }
          });

          // Also check for meta descriptions
          try {
            var metaDesc = document.querySelector('meta[name="description"]');
            if (metaDesc && metaDesc.getAttribute('content')) {
              var metaText = cleanText(metaDesc.getAttribute('content'));
              if (metaText.length > 50) {
                descriptions.push(metaText);
              }
            }
          } catch (e) {
            // Continue if fails
          }

          // Look for paragraphs near "About Us" headings
          var headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
          headings.forEach(function(heading) {
            if (heading.textContent && heading.textContent.toLowerCase().includes('about')) {
              var nextElement = heading.nextElementSibling;
              while (nextElement && descriptions.length < 3) {
                if (nextElement.tagName === 'P' && nextElement.textContent) {
                  var text = cleanText(nextElement.textContent);
                  if (text.length > 50) {
                    descriptions.push(text);
                  }
                }
                nextElement = nextElement.nextElementSibling;
              }
            }
          });

          // Special handling for EZ Rankings - look for the main about content
          var mainContent = document.querySelector('.container');
          if (mainContent) {
            var paragraphs = mainContent.querySelectorAll('p');
            paragraphs.forEach(function(p) {
              if (p.textContent && p.textContent.length > 100) {
                var text = cleanText(p.textContent);
                // Filter out navigation and footer content
                if (!text.toLowerCase().includes('contact') &&
                    !text.toLowerCase().includes('copyright') &&
                    !text.toLowerCase().includes('follow us') &&
                    text.length > 100) {
                  descriptions.push(text);
                }
              }
            });
          }

          // Return the longest description
          if (descriptions.length > 0) {
            descriptions.sort(function(a, b) { return b.length - a.length; });
            return descriptions[0];
          }

          return undefined;
        }

        // Extract mission and vision statements
        function extractMissionVision() {
          var missionKeywords = ['mission', 'our mission', 'purpose', 'why we exist', 'what we do'];
          var visionKeywords = ['vision', 'our vision', 'future', 'goal', 'aspiration', 'where we are going'];

          var headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');

          headings.forEach(function(heading) {
            if (!heading.textContent) return;

            var headingText = heading.textContent.toLowerCase();

            // Check for mission
            if (containsKeywords(headingText, missionKeywords) && !aboutData.missionStatement) {
              var nextElement = heading.nextElementSibling;
              if (nextElement && nextElement.textContent) {
                var text = cleanText(nextElement.textContent);
                if (text.length > 30 && text.length < 1500) {
                  aboutData.missionStatement = text;
                }
              }
            }

            // Check for vision
            if (containsKeywords(headingText, visionKeywords) && !aboutData.visionStatement) {
              var nextElement = heading.nextElementSibling;
              if (nextElement && nextElement.textContent) {
                var text = cleanText(nextElement.textContent);
                if (text.length > 30 && text.length < 1500) {
                  aboutData.visionStatement = text;
                }
              }
            }
          });

          // Also look for mission/vision in specific containers
          var missionSelectors = [
            '.mission',
            '.purpose',
            '[class*="mission"]',
            '[id*="mission"]',
            '.company-mission'
          ];

          var visionSelectors = [
            '.vision',
            '.future',
            '[class*="vision"]',
            '[id*="vision"]',
            '.company-vision'
          ];

          if (!aboutData.missionStatement) {
            missionSelectors.forEach(function(selector) {
              try {
                var element = document.querySelector(selector);
                if (element && element.textContent) {
                  var text = cleanText(element.textContent);
                  if (text.length > 30 && text.length < 1500) {
                    aboutData.missionStatement = text;
                  }
                }
              } catch (e) {
                // Continue if selector fails
              }
            });
          }

          if (!aboutData.visionStatement) {
            visionSelectors.forEach(function(selector) {
              try {
                var element = document.querySelector(selector);
                if (element && element.textContent) {
                  var text = cleanText(element.textContent);
                  if (text.length > 30 && text.length < 1500) {
                    aboutData.visionStatement = text;
                  }
                }
              } catch (e) {
                // Continue if selector fails
              }
            });
          }
        }

        // Extract company values and key points
        function extractValuesAndKeyPoints() {
          // Enhanced keywords for values and principles
          var valueKeywords = ['values', 'principles', 'excellence', 'key to excellence', 'core values', 'beliefs', 'culture', 'philosophy'];
          var headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');

          headings.forEach(function(heading) {
            if (!heading.textContent) return;

            var headingText = heading.textContent.toLowerCase();

            if (containsKeywords(headingText, valueKeywords)) {
              // Look for list items or subsequent elements
              var parent = heading.parentElement;
              if (parent) {
                var listItems = parent.querySelectorAll('li');
                if (listItems.length > 0) {
                  listItems.forEach(function(item) {
                    if (item.textContent) {
                      var text = cleanText(item.textContent);
                      if (text.length > 5 && text.length < 200) {
                        aboutData.companyValues.push(text);
                      }
                    }
                  });
                }

                // Look for div elements with value descriptions
                var valueElements = parent.querySelectorAll('div h3, div h4, .service-clm h4, .value-item, .principle-item');
                valueElements.forEach(function(element) {
                  if (element.textContent) {
                    var text = cleanText(element.textContent);
                    if (text.length > 5 && text.length < 150) {
                      aboutData.keyPoints.push(text);
                    }
                  }
                });

                // Special handling for excellence sections
                var excellenceItems = parent.querySelectorAll('.col-md-2, .col-sm-6, .feature-item, .value-card');
                excellenceItems.forEach(function(item) {
                  var heading = item.querySelector('h3, h4, h5');
                  if (heading && heading.textContent) {
                    var text = cleanText(heading.textContent);
                    if (text.length > 3 && text.length < 100) {
                      aboutData.keyPoints.push(text);
                    }
                  }
                });
              }
            }
          });

          // Also look for service sections that might contain key points
          var serviceElements = document.querySelectorAll('.service-clm h4');
          serviceElements.forEach(function(element) {
            if (element.textContent) {
              var text = cleanText(element.textContent);
              if (text.length > 5 && text.length < 100 &&
                  !text.toLowerCase().includes('seo') &&
                  !text.toLowerCase().includes('marketing')) {
                aboutData.keyPoints.push(text);
              }
            }
          });
        }

        // Extract founding information
        function extractFoundingInfo() {
          var foundingKeywords = ['founded', 'established', 'started', 'since', 'history', 'began'];
          var pageText = document.body.textContent.toLowerCase();

          // Look for founding year patterns
          var yearPattern = /(founded|established|started|since)\\\\s+(in\\\\s+)?(\\\\d{4})/gi;
          var matches = pageText.match(yearPattern);
          if (matches && matches.length > 0) {
            var yearMatch = matches[0].match(/\\\\d{4}/);
            if (yearMatch) {
              aboutData.foundingInfo = {
                year: parseInt(yearMatch[0]),
                description: cleanText(matches[0])
              };
            }
          }

          // Look for founding information in specific sections
          var headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
          headings.forEach(function(heading) {
            if (heading.textContent && containsKeywords(heading.textContent.toLowerCase(), foundingKeywords)) {
              var nextElement = heading.nextElementSibling;
              if (nextElement && nextElement.textContent) {
                var text = cleanText(nextElement.textContent);
                if (text.length > 20 && text.length < 500) {
                  if (!aboutData.foundingInfo) {
                    aboutData.foundingInfo = { description: text };
                  }
                }
              }
            }
          });
        }

        // Extract awards and certifications
        function extractAwardsAndCertifications() {
          var awardKeywords = ['award', 'recognition', 'achievement', 'honor', 'certificate', 'certification', 'accreditation'];
          var awards = [];
          var certifications = [];

          // Look for award sections
          var headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
          headings.forEach(function(heading) {
            if (heading.textContent && containsKeywords(heading.textContent.toLowerCase(), awardKeywords)) {
              var parent = heading.parentElement;
              if (parent) {
                var listItems = parent.querySelectorAll('li, .award-item, .certification-item');
                listItems.forEach(function(item) {
                  if (item.textContent) {
                    var text = cleanText(item.textContent);
                    if (text.length > 10 && text.length < 200) {
                      if (text.toLowerCase().includes('certificate') || text.toLowerCase().includes('certification')) {
                        certifications.push(text);
                      } else {
                        awards.push(text);
                      }
                    }
                  }
                });
              }
            }
          });

          aboutData.awards = awards;
          aboutData.certifications = certifications;
        }

        // Extract office locations
        function extractOfficeLocations() {
          var locationKeywords = ['office', 'location', 'address', 'corporate', 'headquarters', 'branch'];
          var locations = [];

          // Look for address patterns
          var addressElements = document.querySelectorAll('address, [class*="address"], [class*="office"], [class*="location"], [class*="contact"]');
          addressElements.forEach(function(element) {
            if (element.textContent) {
              var text = cleanText(element.textContent);
              if (text.length > 15 && text.length < 300) {
                locations.push(text);
              }
            }
          });

          // Look near location headings
          var headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
          headings.forEach(function(heading) {
            if (heading.textContent && containsKeywords(heading.textContent.toLowerCase(), locationKeywords)) {
              var nextElement = heading.nextElementSibling;
              if (nextElement && nextElement.textContent) {
                var text = cleanText(nextElement.textContent);
                if (text.length > 15 && text.length < 300) {
                  locations.push(text);
                }
              }
            }
          });

          // Remove duplicates
          aboutData.officeLocations = [...new Set(locations)];
        }

        // Execute all extraction functions
        aboutData.companyDescription = extractCompanyDescription();
        extractMissionVision();
        extractValuesAndKeyPoints();
        extractFoundingInfo();
        extractAwardsAndCertifications();
        extractOfficeLocations();

        // Check for global presence indicators
        var pageText = document.body.textContent.toLowerCase();
        aboutData.globalPresence = pageText.includes('global') ||
                                  pageText.includes('international') ||
                                  pageText.includes('worldwide') ||
                                  aboutData.officeLocations.length > 1;

        return aboutData;
      })()
    `);

		return result as AboutData;
	} catch (error) {
		logger.error("Error in extractAboutData:", error);
		return {
			companyDescription: undefined,
			foundingInfo: undefined,
			missionStatement: undefined,
			visionStatement: undefined,
			companyValues: [],
			awards: [],
			industries: [],
			globalPresence: false,
			officeLocations: [],
			certifications: [],
			keyPoints: [],
		};
	}
}
