/* eslint-disable no-undef */
import { Page } from "puppeteer";

import { CompanyInfo } from "../../types/scrapper";

export async function extractAboutAndTeamInfo(
	page: Page,
): Promise<CompanyInfo> {
	console.log("Extracting about page and team information");

	return await page.evaluate(() => {
		const aboutInfo: CompanyInfo = {
			companyDescription: undefined,
			foundingInfo: undefined,
			missionStatement: undefined,
			teamMembers: undefined,
			companyValues: undefined,
			awards: undefined,
			industries: undefined,
		};

		// Extract company description
		const descriptionSelectors = [
			".about-description",
			".company-description",
			".about-content p",
			".about-section p",
			".about-us p",
			"#about p",
			"section.about p",
			'[data-section="about"] p',
		];

		for (const selector of descriptionSelectors) {
			try {
				const elements = document.querySelectorAll(selector);
				if (elements && elements.length > 0) {
					// Get first three paragraphs if available
					const paragraphs = Array.from(elements)
						.slice(0, 3)
						.map((el) => el.textContent?.trim())
						.filter((text) => text && text.length > 50) // Only substantial paragraphs
						.join("\n\n");

					if (paragraphs && paragraphs.length > 100) {
						aboutInfo.companyDescription = paragraphs;
						break;
					}
				}
			} catch (e) {
				console.log("Error extracting company description:", e);
				// Continue if selector fails
			}
		}

		// Extract founding info
		const foundingTextRegex = /found(?:ed|ing)(?:\s+in)?\s+(\d{4})/i;
		const bodyText = document.body.innerText;
		const foundingMatch = bodyText.match(foundingTextRegex);

		if (foundingMatch) {
			// Get the sentence containing the founding info
			const sentences = bodyText.split(/[.!?]+/);
			for (const sentence of sentences) {
				if (sentence.match(foundingTextRegex)) {
					aboutInfo.foundingInfo = sentence.trim();
					break;
				}
			}
		}

		// Extract mission statement
		const missionSelectors = [
			".mission-statement",
			".mission",
			".vision",
			".our-mission",
			"#mission",
			"blockquote",
			'h2:contains("Mission") + p',
			'h3:contains("Mission") + p',
		];

		for (const selector of missionSelectors) {
			try {
				let element;
				if (selector.includes(":contains")) {
					// Handle special case for heading-based selectors
					const parts = selector.split(":contains(");
					const headingType = parts[0];
					const textContent = parts[1]?.replace('") + p', "");

					// Find headings with this text
					const headings = Array.from(
						document.querySelectorAll(headingType || ""),
					);
					const matchingHeading = headings.find((h) =>
						h.textContent?.includes(textContent || ""),
					);

					// Get next paragraph if heading found
					if (matchingHeading) {
						element = matchingHeading.nextElementSibling;
					}
				} else {
					element = document.querySelector(selector);
				}

				if (element && element.textContent?.trim()) {
					const text = element.textContent.trim();
					if (text.length > 20) {
						aboutInfo.missionStatement = text;
						break;
					}
				}
			} catch (e) {
				// Continue if selector fails
				console.log("Error extracting mission statement:", e);
			}
		}

		// Extract team members
		const teamSelectors = [
			".team-member",
			".team-card",
			".member",
			".employee",
			".staff-member",
			".people-card",
			".bio-card",
			".team-grid > div",
			".leadership-team > div",
			".people > div",
		];

		for (const selector of teamSelectors) {
			try {
				const elements = document.querySelectorAll(selector);
				if (elements && elements.length > 0) {
					const teamMembers = Array.from(elements)
						.map((member) => {
							const teamMember: {
								name: string;
								title?: string;
								bio?: string;
								imageUrl?: string;
								contact?: {
									email?: string;
									phone?: string;
									linkedin?: string;
								};
							} = { name: "" };

							// Extract name
							const nameElement = member.querySelector(
								"h2, h3, h4, .name, .member-name, strong",
							);
							if (
								nameElement &&
								nameElement.textContent?.trim()
							) {
								teamMember.name =
									nameElement.textContent.trim();
							}

							// Extract title/position
							const titleElement = member.querySelector(
								".title, .position, .job-title, .role, em",
							);
							if (
								titleElement &&
								titleElement.textContent?.trim()
							) {
								teamMember.title =
									titleElement.textContent.trim();
							}

							// Extract bio
							const bioElement = member.querySelector(
								"p, .bio, .description, .about",
							);
							if (bioElement && bioElement.textContent?.trim()) {
								teamMember.bio = bioElement.textContent.trim();
							}

							// Extract image
							const imageElement = member.querySelector("img");
							if (
								imageElement &&
								imageElement.getAttribute("src")
							) {
								const imgSrc = imageElement.getAttribute("src");
								// Handle relative URLs
								if (imgSrc?.startsWith("/")) {
									try {
										teamMember.imageUrl = new URL(
											imgSrc,
											window.location.origin,
										).href;
									} catch (e) {
										console.log(
											"Error parsing image URL:",
											e,
										);
										teamMember.imageUrl = imgSrc;
									}
								} else {
									teamMember.imageUrl = imgSrc || undefined;
								}
							}

							// Extract contact info
							const contact: {
								email?: string;
								phone?: string;
								linkedin?: string;
							} = {};

							// Email
							const emailElement =
								member.querySelector('a[href^="mailto:"]');
							if (emailElement) {
								const mailtoHref =
									emailElement.getAttribute("href");
								if (mailtoHref) {
									contact.email = mailtoHref
										?.replace("mailto:", "")
										?.split("?")[0]
										?.trim();
								}
							}

							// Phone
							const phoneElement =
								member.querySelector('a[href^="tel:"]');
							if (phoneElement) {
								const telHref =
									phoneElement.getAttribute("href");
								if (telHref) {
									contact.phone = telHref
										.replace("tel:", "")
										.trim();
								}
							}

							// LinkedIn
							const linkedinElement = member.querySelector(
								'a[href*="linkedin.com"]',
							);
							if (linkedinElement) {
								contact.linkedin =
									linkedinElement.getAttribute("href") ||
									undefined;
							}

							if (Object.keys(contact).length > 0) {
								teamMember.contact = contact;
							}

							return teamMember;
						})
						.filter((member) => member.name); // Only include members with names

					if (teamMembers.length > 0) {
						aboutInfo.teamMembers = teamMembers;
						break;
					}
				}
			} catch (e) {
				console.log("Error extracting team members:", e);
				// Continue if selector fails
			}
		}

		// Extract company values
		const valuesSelectors = [
			".values",
			".core-values",
			".our-values",
			".value-item",
			"#values",
			".principles",
			'h2:contains("Values") + ul li',
			'h3:contains("Values") + ul li',
		];

		for (const selector of valuesSelectors) {
			try {
				let elements;
				if (selector.includes(":contains")) {
					// Handle special case for heading-based selectors
					const parts = selector.split(":contains(");
					const headingType = parts[0];
					const textContent = parts[1]?.split('") + ')[0];
					const targetElement = parts[1]?.split('") + ')[1];

					// Find headings with this text
					const headings = Array.from(
						document.querySelectorAll(headingType || ""),
					);
					const matchingHeading = headings.find((h) =>
						h.textContent?.includes(textContent || ""),
					);

					// Get list items if heading found
					if (matchingHeading && matchingHeading.nextElementSibling) {
						const nextElement = matchingHeading.nextElementSibling;
						if (
							nextElement.tagName.toLowerCase() === targetElement
						) {
							elements = nextElement.querySelectorAll("li");
						}
					}
				} else {
					elements = document.querySelectorAll(selector);
				}

				if (elements && elements.length > 0) {
					const values = Array.from(elements)
						.map((el) => el.textContent?.trim())
						.filter((text) => text) as string[];

					if (values.length > 0) {
						aboutInfo.companyValues = values;
						break;
					}
				}
			} catch (e) {
				console.log("Error extracting company values:", e);
				// Continue if selector fails
			}
		}

		// Extract awards or recognition
		const awardsSelectors = [
			".awards",
			".recognition",
			".achievements",
			".award-item",
			"#awards",
			".honors",
			'h2:contains("Award") + ul li',
			'h3:contains("Award") + ul li',
		];

		for (const selector of awardsSelectors) {
			try {
				let elements;
				if (selector.includes(":contains")) {
					// Similar handling as values section
					const parts = selector.split(":contains(");
					const headingType = parts[0];
					const textContent = parts[1]?.split('") + ')[0];
					const targetElement = parts[1]?.split('") + ')[1];

					const headings = Array.from(
						document.querySelectorAll(headingType || ""),
					);
					const matchingHeading = headings.find((h) =>
						h.textContent?.includes(textContent || ""),
					);

					if (matchingHeading && matchingHeading.nextElementSibling) {
						const nextElement = matchingHeading.nextElementSibling;
						if (
							nextElement.tagName.toLowerCase() === targetElement
						) {
							elements = nextElement.querySelectorAll("li");
						}
					}
				} else {
					elements = document.querySelectorAll(selector);
				}

				if (elements && elements.length > 0) {
					const awards = Array.from(elements)
						.map((el) => el.textContent?.trim())
						.filter((text) => text) as string[];

					if (awards.length > 0) {
						aboutInfo.awards = awards;
						break;
					}
				}
			} catch (e) {
				console.log("Error extracting awards:", e);
				// Continue if selector fails
			}
		}

		// Extract industries or sectors
		const industrySelectors = [
			".industries",
			".sectors",
			".specialties",
			".expertise",
			"#industries",
			".markets",
			'h2:contains("Industries") + ul li',
			'h3:contains("Industries") + ul li',
		];

		for (const selector of industrySelectors) {
			try {
				let elements;
				if (selector.includes(":contains")) {
					// Similar handling as previous sections
					const parts = selector.split(":contains(");
					const headingType = parts[0];
					const textContent = parts[1]?.split('") + ')[0];
					const targetElement = parts[1]?.split('") + ')[1];

					const headings = Array.from(
						document.querySelectorAll(headingType || ""),
					);
					const matchingHeading = headings.find((h) =>
						h.textContent?.includes(textContent || ""),
					);

					if (matchingHeading && matchingHeading.nextElementSibling) {
						const nextElement = matchingHeading.nextElementSibling;
						if (
							nextElement.tagName.toLowerCase() === targetElement
						) {
							elements = nextElement.querySelectorAll("li");
						}
					}
				} else {
					elements = document.querySelectorAll(selector);
				}

				if (elements && elements.length > 0) {
					const industries = Array.from(elements)
						.map((el) => el.textContent?.trim())
						.filter((text) => text) as string[];

					if (industries.length > 0) {
						aboutInfo.industries = industries;
						break;
					}
				}
			} catch (e) {
				console.log("Error extracting industries:", e);
				// Continue if selector fails
			}
		}

		return aboutInfo;
	});
}

export async function findAboutAndTeamPages(
	page: Page,
	baseUrl: string,
	nestedLinks?: string[],
): Promise<string[]> {
	console.log("Finding about and team pages");

	// First, check the provided nestedLinks if available
	if (nestedLinks && nestedLinks.length > 0) {
		// Primary keywords - these are highly specific to about/team pages
		const primaryKeywords = [
			"about-us",
			"about us",
			"our story",
			"who we are",
			"life at",
			"our team",
			"meet the team",
			"team",
			"our history",
		];

		// Secondary keywords - only use these for path-exact matches to avoid false positives
		const secondaryKeywords = [
			"about",
			"team",
			"people",
			"leadership",
			"management",
			"executives",
			"founders",
			"story",
			"culture",
		];

		// Filter the nestedLinks
		const aboutLinks = nestedLinks
			.filter((url) => {
				try {
					const linkUrl = new URL(url);
					const baseUrlObj = new URL(baseUrl);

					// Only include links from the same domain
					if (linkUrl.hostname !== baseUrlObj.hostname) {
						return false;
					}

					// Convert URL path to lowercase for keyword matching
					const path = linkUrl.pathname.toLowerCase();
					const pathSegments = path.split("/").filter(Boolean);
					const lastSegment =
						pathSegments[pathSegments.length - 1] || "";
					const filenameWithoutExt = lastSegment.replace(
						/\.[^/.]+$/,
						"",
					); // Remove extension

					// Check if the last URL segment exactly matches a primary keyword (with hyphens or not)
					for (const keyword of primaryKeywords) {
						const keywordNoSpaces = keyword.replace(/\s+/g, "");
						const keywordWithHyphens = keyword.replace(/\s+/g, "-");

						if (
							filenameWithoutExt === keywordNoSpaces ||
							filenameWithoutExt === keywordWithHyphens
						) {
							return true;
						}
					}

					// For exact matches of any keyword in the URL's last segment
					for (const keyword of [
						...primaryKeywords,
						...secondaryKeywords,
					]) {
						const keywordNoSpaces = keyword.replace(/\s+/g, "");
						const keywordWithHyphens = keyword.replace(/\s+/g, "-");

						// Check for exact matches - the keyword must be the entire filename
						// or at least start/end with it (to catch cases like "about-us" or "our-team")
						if (
							filenameWithoutExt === keywordNoSpaces ||
							filenameWithoutExt === keywordWithHyphens ||
							filenameWithoutExt.startsWith(
								`${keywordWithHyphens}-`,
							) ||
							filenameWithoutExt.endsWith(
								`-${keywordWithHyphens}`,
							)
						) {
							return true;
						}
					}

					// Special case for "life-at-" pattern which is common for team/culture pages
					if (filenameWithoutExt.startsWith("life-at-")) {
						return true;
					}

					return false;
				} catch (e) {
					console.log(`Error parsing URL: ${url}`, e);
					return false;
				}
			})
			.sort((a, b) => {
				// Prioritize "about-us" above all else
				const aIsAboutUs =
					a.toLowerCase().includes("about-us") ||
					a.toLowerCase().includes("aboutus");
				const bIsAboutUs =
					b.toLowerCase().includes("about-us") ||
					b.toLowerCase().includes("aboutus");

				if (aIsAboutUs && !bIsAboutUs) return -1;
				if (!aIsAboutUs && bIsAboutUs) return 1;

				// Next priority for "life-at" pages
				const aIsLifeAt = a.toLowerCase().includes("life-at");
				const bIsLifeAt = b.toLowerCase().includes("life-at");

				if (aIsLifeAt && !bIsLifeAt) return -1;
				if (!aIsLifeAt && bIsLifeAt) return 1;

				// Next priority for team pages
				const aIsTeam = a.toLowerCase().includes("team");
				const bIsTeam = b.toLowerCase().includes("team");

				if (aIsTeam && !bIsTeam) return -1;
				if (!aIsTeam && bIsTeam) return 1;

				// Default to shortest URL (likely more relevant)
				return a.length - b.length;
			});

		if (aboutLinks.length > 0) {
			return aboutLinks;
		}
	}

	// Fallback to DOM scraping if nestedLinks didn't yield results
	return await page.evaluate((baseUrl) => {
		const aboutPageKeywords = [
			"about-us",
			"about us",
			"our story",
			"who we are",
			"our team",
			"team",
			"meet our team",
			"leadership",
			"management",
			"executives",
			"founders",
			"our history",
			"our people",
		];

		const aboutLinks: string[] = [];

		// Find all links
		document.querySelectorAll("a").forEach((link) => {
			const href = link.getAttribute("href");
			const text = link.textContent?.toLowerCase().trim() || "";

			if (!href) return;

			// Check if link text exactly matches a keyword
			const exactTextMatch = aboutPageKeywords.some(
				(keyword) =>
					text === keyword ||
					// Also match with capitalization variations
					text === keyword.charAt(0).toUpperCase() + keyword.slice(1),
			);

			// Check if href contains an about keyword as a distinct segment
			const hrefLower = href.toLowerCase();
			const urlKeywordMatch = aboutPageKeywords.some((keyword) => {
				const keywordNoSpaces = keyword.replace(/\s+/g, "");
				const keywordWithHyphens = keyword.replace(/\s+/g, "-");

				const urlSegments = hrefLower.split("/").filter(Boolean);
				const lastSegment = urlSegments[urlSegments.length - 1] || "";
				const segmentWithoutExt = lastSegment.replace(/\.[^/.]+$/, "");

				return (
					segmentWithoutExt === keywordNoSpaces ||
					segmentWithoutExt === keywordWithHyphens ||
					segmentWithoutExt.startsWith(`${keywordWithHyphens}-`) ||
					segmentWithoutExt.endsWith(`-${keywordWithHyphens}`)
				);
			});

			// Special case for "life-at" pattern
			const isLifeAtPage = hrefLower.includes("/life-at-");

			if (exactTextMatch || urlKeywordMatch || isLifeAtPage) {
				try {
					let fullUrl = href;

					// Handle relative URLs
					if (href.startsWith("/")) {
						const url = new URL(baseUrl);
						fullUrl = `${url.origin}${href}`;
					} else if (!href.startsWith("http")) {
						fullUrl = `${baseUrl}${href.startsWith("/") ? "" : "/"}${href}`;
					}

					// Only consider URLs from the same domain
					const linkUrl = new URL(fullUrl);
					const baseUrlObj = new URL(baseUrl);

					if (linkUrl.hostname === baseUrlObj.hostname) {
						aboutLinks.push(fullUrl);
					}
				} catch (e) {
					console.log(`Error parsing URL: ${href}`, e);
					// Skip invalid URLs
				}
			}
		});

		// Sort links by priority
		return aboutLinks.sort((a, b) => {
			// Prioritize URLs with "about-us" in them
			const aIsAboutUs =
				a.toLowerCase().includes("about-us") ||
				a.toLowerCase().includes("aboutus");
			const bIsAboutUs =
				b.toLowerCase().includes("about-us") ||
				b.toLowerCase().includes("aboutus");

			if (aIsAboutUs && !bIsAboutUs) return -1;
			if (!aIsAboutUs && bIsAboutUs) return 1;

			// Next priority for "life-at" pages
			const aIsLifeAt = a.toLowerCase().includes("life-at");
			const bIsLifeAt = b.toLowerCase().includes("life-at");

			if (aIsLifeAt && !bIsLifeAt) return -1;
			if (!aIsLifeAt && bIsLifeAt) return 1;

			// Default to shortest URL (likely more relevant)
			return a.length - b.length;
		});
	}, baseUrl);
}

export function isAboutDetailsComplete(details: CompanyInfo): boolean {
	// Consider details complete if we have company description, team members,
	// and at least one of the following optional fields
	const hasDescription = Boolean(details.companyDescription);
	const hasTeamMembers = Boolean(
		details.teamMembers && details.teamMembers.length > 0,
	);

	// Check for at least one optional field
	const hasOptionalField = Boolean(
		details.foundingInfo ||
			details.missionStatement ||
			(details.companyValues && details.companyValues.length > 0) ||
			(details.awards && details.awards.length > 0) ||
			(details.industries && details.industries.length > 0),
	);

	// Return true if we have description, team members, and at least one optional field
	return hasDescription && hasTeamMembers && hasOptionalField;
}

export function mergeCompanyInfo(
	existing: CompanyInfo,
	additional: CompanyInfo,
): CompanyInfo {
	const merged: CompanyInfo = { ...existing };

	// Use additional company description if existing one is missing or shorter
	if (additional.companyDescription) {
		if (
			!existing.companyDescription ||
			additional.companyDescription.length >
				existing.companyDescription.length
		) {
			merged.companyDescription = additional.companyDescription;
		}
	}

	// Use additional founding info if existing one is missing
	if (!existing.foundingInfo && additional.foundingInfo) {
		merged.foundingInfo = additional.foundingInfo;
	}

	// Use additional mission statement if existing one is missing
	if (!existing.missionStatement && additional.missionStatement) {
		merged.missionStatement = additional.missionStatement;
	}

	// Merge team members, eliminating duplicates based on name
	if (additional.teamMembers && additional.teamMembers.length > 0) {
		const existingMembers = existing.teamMembers || [];
		const existingNames = new Set(
			existingMembers.map((member) => member.name),
		);

		const newMembers = additional.teamMembers.filter(
			(member) => !existingNames.has(member.name),
		);

		merged.teamMembers = [...existingMembers, ...newMembers];
	}

	// Merge company values
	if (additional.companyValues && additional.companyValues.length > 0) {
		merged.companyValues = Array.from(
			new Set([
				...(existing.companyValues || []),
				...additional.companyValues,
			]),
		);
	}

	// Merge awards
	if (additional.awards && additional.awards.length > 0) {
		merged.awards = Array.from(
			new Set([...(existing.awards || []), ...additional.awards]),
		);
	}

	// Merge industries
	if (additional.industries && additional.industries.length > 0) {
		merged.industries = Array.from(
			new Set([...(existing.industries || []), ...additional.industries]),
		);
	}

	return merged;
}
