/* eslint-disable no-undef */
import { Page } from 'puppeteer';

export async function getTitleAndDesc(page: Page) {
	console.log("Getting title and desc of the page");
	const result = await page.evaluate(() => {
		let title = document.head.querySelector("title")?.innerHTML || null,
			desc = null;
		document.head.querySelectorAll("meta").forEach((meta) => {
			const metaPropertyName =
				meta.getAttribute("name") ||
				meta.getAttribute("property") ||
				"";
			if (["title", "og:title"].includes(metaPropertyName))
				title = meta.getAttribute("content");
			else if (
				["description", "og:description"].includes(metaPropertyName)
			)
				desc = meta.getAttribute("content");
		});
		return {
			title,
			desc,
		};
	});
	console.log(`Title - ${result.title}\nDesc - ${result.desc}`);
	return result;
}

export async function getNestedLinks(
	page: Page,
	normalizeURLFn: (url: string) => string,
) {
	console.log("Getting all nested links in the page");
	const nestedAnchorsHrefs = await page.evaluate(() => {
		const nestedAnchors = document.querySelectorAll("a");
		const nestedAnchorsHrefs: string[] = [];
		nestedAnchors.forEach((nestedAnchor) =>
			nestedAnchorsHrefs.push(nestedAnchor.href),
		);
		return nestedAnchorsHrefs;
	});

	const currentPageURL = new URL(normalizeURLFn(page.url()));
	const nestedLinks = new Set<string>();

	nestedAnchorsHrefs.forEach((href) => {
		let nestedAnchorHref = href;
		let nestedURL: URL;
		try {
			nestedURL = new URL(normalizeURLFn(href));
		} catch {
			nestedAnchorHref = `${currentPageURL.origin}${
				nestedAnchorHref.startsWith("/") ? "" : "/"
			}${nestedAnchorHref}`;
			try {
				nestedURL = new URL(normalizeURLFn(nestedAnchorHref));
			} catch {
				return;
			}
		}
		if (
			nestedURL.origin === currentPageURL.origin &&
			nestedURL.href !== currentPageURL.href
		)
			nestedLinks.add(nestedURL.href);
	});

	console.log(`Nested links: ${Array.from(nestedLinks)}`);
	return nestedLinks;
}

export async function removeUnnecessaryElements(
	page: Page,
	elementsToRemove: string[],
) {
	console.log("Removing unnecessary elements for the page");
	const removedCount = await page.evaluate((elementsToRemove) => {
		let removedCount = 0;
		document.querySelectorAll(elementsToRemove.join(", ")).forEach((el) => {
			removedCount++;
			el.remove();
		});
		return removedCount;
	}, elementsToRemove);
	console.log(`Removed ${removedCount} unnecessary elements from the page`);
}
