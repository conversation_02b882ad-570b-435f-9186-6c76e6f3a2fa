/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-undef */

import { Page } from 'puppeteer';

import logger from '../utils/logger';

/**
 * Interface representing a team member with their details
 */
export interface TeamMember {
	name: string;
	title?: string;
	bio?: string;
	imageUrl?: string;
	contact?: {
		email?: string;
		phone?: string;
		linkedin?: string;
		skype?: string;
	};
}

/**
 * Extracts team information from a webpage
 * @param page - Puppeteer page instance
 * @returns Promise containing an array of team members
 */
export async function extractTeamData(page: Page): Promise<TeamMember[]> {
	logger.info("Extracting team member information");

	try {
		// Use string-based evaluation to avoid TypeScript transpilation issues
		const result = await page.evaluate(`
      (function() {
        var teamMembers = [];

        // Helper function to clean text
        function cleanText(text) {
          if (!text) return '';
          return text.replace(/\\s+/g, ' ').trim();
        }

        // Helper function to extract email from text
        function extractEmail(text) {
          if (!text) return null;
          var emailMatch = text.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/);
          return emailMatch ? emailMatch[0] : null;
        }

        // Helper function to extract phone from text
        function extractPhone(text) {
          if (!text) return null;
          var phoneMatch = text.match(/[+]?[0-9\\s\\-\\(\\)]{10,}/);
          return phoneMatch ? phoneMatch[0].trim() : null;
        }

        // Helper function to extract LinkedIn URL
        function extractLinkedIn(element) {
          var links = element.querySelectorAll('a[href*="linkedin"]');
          if (links.length > 0) {
            return links[0].href;
          }
          return null;
        }

        // Helper function to extract Skype
        function extractSkype(element) {
          var links = element.querySelectorAll('a[href*="skype"]');
          if (links.length > 0) {
            return links[0].href;
          }
          return null;
        }

        // Helper function to extract image URL
        function extractImageUrl(element) {
          var img = element.querySelector('img');
          if (img && img.src) {
            return img.src;
          }
          return null;
        }

        // Function to extract team members from board/leadership sections
        function extractBoardMembers() {
          var boardKeywords = ['board', 'directors', 'leadership', 'management', 'executives'];
          var headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');

          headings.forEach(function(heading) {
            if (!heading.textContent) return;

            var headingText = heading.textContent.toLowerCase();
            var isBoard = boardKeywords.some(function(keyword) {
              return headingText.includes(keyword);
            });

            if (isBoard) {
              // Look for team member containers after this heading
              var container = heading.parentElement;
              if (container) {
                // First try to find specific designation containers (for EZ Rankings structure)
                var designationContainers = container.querySelectorAll('.designation');
                if (designationContainers.length > 0) {
                  designationContainers.forEach(function(designationDiv) {
                    var member = extractMemberFromDesignationDiv(designationDiv);
                    if (member && member.name) {
                      // Check for duplicates before adding
                      var exists = teamMembers.some(function(existing) {
                        return existing.name.toLowerCase() === member.name.toLowerCase();
                      });
                      if (!exists) {
                        teamMembers.push(member);
                      }
                    }
                  });
                } else {
                  // Fallback to general extraction
                  extractTeamMembersFromContainer(container);
                }
              }
            }
          });
        }

        // Function to extract member from designation div (specific to EZ Rankings structure)
        function extractMemberFromDesignationDiv(designationDiv) {
          var member = {
            name: '',
            title: undefined,
            bio: undefined,
            imageUrl: undefined,
            contact: {}
          };

          // Extract name from H3 within designation div
          var nameElement = designationDiv.querySelector('h3');
          if (nameElement && nameElement.textContent) {
            member.name = cleanText(nameElement.textContent);
          }

          // Extract title from H5 within designation div
          var titleElement = designationDiv.querySelector('h5');
          if (titleElement && titleElement.textContent) {
            member.title = cleanText(titleElement.textContent);
          }

          // Extract contact information from links within designation div
          var links = designationDiv.querySelectorAll('a');
          links.forEach(function(link) {
            if (link.href) {
              if (link.href.includes('linkedin')) {
                member.contact.linkedin = link.href;
              } else if (link.href.includes('mailto:')) {
                member.contact.email = link.href.replace('mailto:', '');
              } else if (link.href.includes('skype:')) {
                member.contact.skype = link.href;
              }
            }
          });

          // Look for bio in the parent container
          var parentContainer = designationDiv.parentElement;
          if (parentContainer) {
            var bioElements = parentContainer.querySelectorAll('p');
            bioElements.forEach(function(bioElement) {
              if (bioElement.textContent && bioElement.textContent.length > 100) {
                member.bio = cleanText(bioElement.textContent);
              }
            });

            // Look for image in the parent container
            var img = parentContainer.querySelector('img');
            if (img && img.src) {
              member.imageUrl = img.src;
            }
          }

          // Clean up contact object
          if (Object.keys(member.contact).length === 0) {
            member.contact = undefined;
          }

          return member;
        }

        // Function to extract team members from a container
        function extractTeamMembersFromContainer(container) {
          // Look for individual team member elements
          var memberSelectors = [
            '.team-member',
            '.member',
            '.director',
            '.executive',
            '.leadership-member',
            '[class*="team"]',
            '[class*="member"]'
          ];

          memberSelectors.forEach(function(selector) {
            try {
              var members = container.querySelectorAll(selector);
              members.forEach(function(memberElement) {
                var member = extractMemberFromElement(memberElement);
                if (member && member.name) {
                  teamMembers.push(member);
                }
              });
            } catch (e) {
              // Continue if selector fails
            }
          });

          // Also look for structured content with names and titles
          var potentialMembers = container.querySelectorAll('div, section, article');
          potentialMembers.forEach(function(element) {
            if (element.children.length > 0) {
              var member = extractMemberFromElement(element);
              if (member && member.name && member.name.length > 2) {
                // Check if we already have this member
                var exists = teamMembers.some(function(existing) {
                  return existing.name.toLowerCase() === member.name.toLowerCase();
                });
                if (!exists) {
                  teamMembers.push(member);
                }
              }
            }
          });
        }

        // Function to extract member details from an element
        function extractMemberFromElement(element) {
          var member = {
            name: '',
            title: undefined,
            bio: undefined,
            imageUrl: undefined,
            contact: {}
          };

          // Extract name - look for headings or strong text
          var nameSelectors = ['h1, h2, h3, h4, h5, h6', 'strong', '.name', '.member-name'];
          for (var i = 0; i < nameSelectors.length; i++) {
            var nameElement = element.querySelector(nameSelectors[i]);
            if (nameElement && nameElement.textContent) {
              var nameText = cleanText(nameElement.textContent);
              // Basic validation for names
              if (nameText.length > 2 && nameText.length < 50 && 
                  !nameText.toLowerCase().includes('director') &&
                  !nameText.toLowerCase().includes('managing') &&
                  nameText.split(' ').length >= 2) {
                member.name = nameText;
                break;
              }
            }
          }

          // If no name found in headings, try to find it in text content
          if (!member.name) {
            var textContent = element.textContent;
            if (textContent) {
              var lines = textContent.split('\\n');
              for (var j = 0; j < lines.length; j++) {
                var line = cleanText(lines[j]);
                if (line.length > 5 && line.length < 50 && 
                    line.split(' ').length >= 2 &&
                    !line.toLowerCase().includes('director') &&
                    !line.toLowerCase().includes('managing')) {
                  member.name = line;
                  break;
                }
              }
            }
          }

          // Extract title - look for role/position indicators
          var titleSelectors = ['.title', '.position', '.role', 'em', 'i'];
          for (var k = 0; k < titleSelectors.length; k++) {
            var titleElement = element.querySelector(titleSelectors[k]);
            if (titleElement && titleElement.textContent) {
              var titleText = cleanText(titleElement.textContent);
              if (titleText.length > 3 && titleText.length < 100) {
                member.title = titleText;
                break;
              }
            }
          }

          // If no title found in specific elements, look for common title patterns
          if (!member.title && element.textContent) {
            var text = element.textContent.toLowerCase();
            var titlePatterns = ['director', 'manager', 'ceo', 'cto', 'cfo', 'president', 'vice president'];
            for (var l = 0; l < titlePatterns.length; l++) {
              if (text.includes(titlePatterns[l])) {
                member.title = titlePatterns[l].charAt(0).toUpperCase() + titlePatterns[l].slice(1);
                break;
              }
            }
          }

          // Extract bio - look for longer text content
          var bioSelectors = ['.bio', '.description', '.about', 'p'];
          for (var m = 0; m < bioSelectors.length; m++) {
            var bioElement = element.querySelector(bioSelectors[m]);
            if (bioElement && bioElement.textContent) {
              var bioText = cleanText(bioElement.textContent);
              if (bioText.length > 50) {
                member.bio = bioText;
                break;
              }
            }
          }

          // Extract contact information
          var elementText = element.textContent || '';
          
          // Extract email
          var email = extractEmail(elementText);
          if (email) {
            member.contact.email = email;
          }

          // Extract phone
          var phone = extractPhone(elementText);
          if (phone) {
            member.contact.phone = phone;
          }

          // Extract LinkedIn
          var linkedin = extractLinkedIn(element);
          if (linkedin) {
            member.contact.linkedin = linkedin;
          }

          // Extract Skype
          var skype = extractSkype(element);
          if (skype) {
            member.contact.skype = skype;
          }

          // Extract image
          member.imageUrl = extractImageUrl(element);

          // Clean up contact object
          if (Object.keys(member.contact).length === 0) {
            member.contact = undefined;
          }

          return member;
        }

        // Main extraction logic
        extractBoardMembers();

        // Also try to find team members in common team sections
        var teamSections = document.querySelectorAll('[class*="team"], [id*="team"], [class*="about"]');
        teamSections.forEach(function(section) {
          extractTeamMembersFromContainer(section);
        });

        // Remove duplicates and invalid entries
        var uniqueMembers = [];
        teamMembers.forEach(function(member) {
          if (member.name && member.name.length > 2) {
            var exists = uniqueMembers.some(function(existing) {
              return existing.name.toLowerCase() === member.name.toLowerCase();
            });
            if (!exists) {
              uniqueMembers.push(member);
            }
          }
        });

        return uniqueMembers;
      })()
    `);

		return result as TeamMember[];
	} catch (error) {
		logger.error("Error in extractTeamData:", error);
		return [];
	}
}
