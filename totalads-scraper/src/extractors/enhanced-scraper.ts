/* eslint-disable @typescript-eslint/no-explicit-any */

import { Page } from 'puppeteer';

import logger from '../utils/logger';
import { extractAboutData } from './about-data-extractor';
import { extractTeamData, TeamMember } from './team-data-extractor';

/**
 * Combined interface for enhanced scraping results
 */
export interface EnhancedAboutData {
	companyDescription?: string;
	foundingInfo?: string;
	missionStatement?: string;
	visionStatement?: string;
	companyValues?: string[];
	awards?: string[];
	industries?: string[];
	globalPresence?: boolean;
	officeLocations?: string[];
	certifications?: string[];
	keyPoints?: string[];
	teamMembers?: TeamMember[];
}

/**
 * Enhanced scraper that extracts both about data and team information
 * @param page - Puppeteer page instance
 * @param url - URL being scraped (for logging)
 * @returns Promise containing enhanced about data with team information
 */
export async function extractEnhancedAboutData(
	page: Page,
	url?: string,
): Promise<EnhancedAboutData> {
	logger.info(
		`Starting enhanced about data extraction${url ? ` for ${url}` : ""}`,
	);

	try {
		// Extract about data and team data in parallel for better performance
		const [aboutData, teamData] = await Promise.all([
			extractAboutData(page),
			extractTeamData(page),
		]);

		// Combine the results
		const enhancedData: EnhancedAboutData = {
			...aboutData,
			teamMembers: teamData,
		};

		// Log extraction results
		logger.info(`Enhanced extraction completed:`, {
			hasDescription: !!enhancedData.companyDescription,
			hasMission: !!enhancedData.missionStatement,
			hasVision: !!enhancedData.visionStatement,
			valuesCount: enhancedData.companyValues?.length || 0,
			keyPointsCount: enhancedData.keyPoints?.length || 0,
			officeLocationsCount: enhancedData.officeLocations?.length || 0,
			teamMembersCount: enhancedData.teamMembers?.length || 0,
			globalPresence: enhancedData.globalPresence,
		});

		return enhancedData;
	} catch (error) {
		logger.error("Error in extractEnhancedAboutData:", error);
		return {
			companyDescription: undefined,
			foundingInfo: undefined,
			missionStatement: undefined,
			visionStatement: undefined,
			companyValues: [],
			awards: [],
			industries: [],
			globalPresence: false,
			officeLocations: [],
			certifications: [],
			keyPoints: [],
			teamMembers: [],
		};
	}
}

/**
 * Extracts enhanced about data from multiple pages
 * @param page - Puppeteer page instance
 * @param baseUrl - Base URL of the website
 * @param aboutPageUrls - Array of about page URLs to visit
 * @param config - Configuration options
 * @returns Promise containing merged enhanced about data
 */
export async function extractEnhancedAboutDataFromPages(
	page: Page,
	baseUrl: string,
	aboutPageUrls: string[],
	config: { maxPagesToVisit?: number; timeout?: number } = {},
): Promise<EnhancedAboutData> {
	logger.info(
		`Extracting enhanced about data from ${aboutPageUrls.length} pages`,
	);

	const maxPages = config.maxPagesToVisit || 3;
	const timeout = config.timeout || 30000;

	let combinedData: EnhancedAboutData = {
		companyDescription: undefined,
		foundingInfo: undefined,
		missionStatement: undefined,
		visionStatement: undefined,
		companyValues: [],
		awards: [],
		industries: [],
		globalPresence: false,
		officeLocations: [],
		certifications: [],
		keyPoints: [],
		teamMembers: [],
	};

	// Limit the number of pages to visit
	const pagesToVisit = aboutPageUrls.slice(0, maxPages);

	for (const pageUrl of pagesToVisit) {
		try {
			logger.info(`Visiting page: ${pageUrl}`);

			// Navigate to the page
			await page.goto(pageUrl, {
				waitUntil: "networkidle2",
				timeout: timeout,
			});

			// Extract data from this page
			const pageData = await extractEnhancedAboutData(page, pageUrl);

			// Merge with existing data
			combinedData = mergeEnhancedAboutData(combinedData, pageData);

			// Check if we have enough information to stop early
			if (isEnhancedDataComplete(combinedData)) {
				logger.info("Collected complete enhanced data, stopping early");
				break;
			}
		} catch (error: any) {
			logger.error(`Error extracting from ${pageUrl}: ${error.message}`);
			// Continue with next page on error
		}
	}

	return combinedData;
}

/**
 * Merges two enhanced about data objects, preferring non-empty values
 */
function mergeEnhancedAboutData(
	existing: EnhancedAboutData,
	newData: EnhancedAboutData,
): EnhancedAboutData {
	return {
		companyDescription:
			existing.companyDescription || newData.companyDescription,
		foundingInfo: existing.foundingInfo || newData.foundingInfo,
		missionStatement: existing.missionStatement || newData.missionStatement,
		visionStatement: existing.visionStatement || newData.visionStatement,
		companyValues: mergeArrays(
			existing.companyValues,
			newData.companyValues,
		),
		awards: mergeArrays(existing.awards, newData.awards),
		industries: mergeArrays(existing.industries, newData.industries),
		globalPresence: existing.globalPresence || newData.globalPresence,
		officeLocations: mergeArrays(
			existing.officeLocations,
			newData.officeLocations,
		),
		certifications: mergeArrays(
			existing.certifications,
			newData.certifications,
		),
		keyPoints: mergeArrays(existing.keyPoints, newData.keyPoints),
		teamMembers: mergeTeamMembers(
			existing.teamMembers,
			newData.teamMembers,
		),
	};
}

/**
 * Merges two arrays, removing duplicates
 */
function mergeArrays(arr1?: string[], arr2?: string[]): string[] {
	const combined = [...(arr1 || []), ...(arr2 || [])];
	return [...new Set(combined)]; // Remove duplicates
}

/**
 * Merges team member arrays, avoiding duplicates based on name
 */
function mergeTeamMembers(
	existing?: TeamMember[],
	newMembers?: TeamMember[],
): TeamMember[] {
	const combined = [...(existing || [])];

	(newMembers || []).forEach((newMember) => {
		const existingIndex = combined.findIndex(
			(existing) =>
				existing.name.toLowerCase() === newMember.name.toLowerCase(),
		);

		if (existingIndex === -1) {
			// New member, add to list
			combined.push(newMember);
		} else {
			// Merge member data, preferring non-empty values
			const existingMember = combined[existingIndex];
			if (existingMember) {
				combined[existingIndex] = {
					name: existingMember.name,
					title: existingMember.title || newMember.title,
					bio: existingMember.bio || newMember.bio,
					imageUrl: existingMember.imageUrl || newMember.imageUrl,
					contact: {
						...existingMember.contact,
						...newMember.contact,
					},
				};
			}
		}
	});

	return combined;
}

/**
 * Checks if enhanced data collection is complete enough to stop early
 */
function isEnhancedDataComplete(data: EnhancedAboutData): boolean {
	return !!(
		data.companyDescription &&
		(data.missionStatement || data.visionStatement) &&
		(data.companyValues?.length || 0) > 0 &&
		(data.teamMembers?.length || 0) > 0 &&
		(data.officeLocations?.length || 0) > 0
	);
}

/**
 * Finds about and team pages on a website
 * @param page - Puppeteer page instance
 * @param baseUrl - Base URL of the website
 * @param existingLinks - Array of existing links found on the page
 * @returns Promise containing array of about/team page URLs
 */
export async function findAboutAndTeamPages(
	page: Page,
	baseUrl: string,
	existingLinks: string[],
): Promise<string[]> {
	logger.info(`Finding about and team pages for ${baseUrl}`);

	try {
		const aboutPageKeywords = [
			"about",
			"about-us",
			"about_us",
			"team",
			"our-team",
			"our_team",
			"leadership",
			"management",
			"directors",
			"board",
			"who-we-are",
			"company",
			"our-story",
			"mission",
			"vision",
			"values",
		];

		const aboutPages: string[] = [];
		const seenUrls = new Set<string>();

		// Filter existing links for about/team pages
		existingLinks.forEach((link) => {
			try {
				const url = new URL(link, baseUrl);
				const path = url.pathname.toLowerCase();
				const href = url.href.toLowerCase();

				// Check if URL contains about/team keywords
				const isAboutPage = aboutPageKeywords.some(
					(keyword) =>
						path.includes(keyword) || href.includes(keyword),
				);

				if (isAboutPage && !seenUrls.has(url.href)) {
					aboutPages.push(url.href);
					seenUrls.add(url.href);
				}
			} catch (e) {
				console.log("Error invalid URL", e);
				// Skip invalid URLs
			}
		});

		logger.info(`Found ${aboutPages.length} potential about/team pages`);
		return aboutPages;
	} catch (error) {
		logger.error("Error finding about and team pages:", error);
		return [];
	}
}
