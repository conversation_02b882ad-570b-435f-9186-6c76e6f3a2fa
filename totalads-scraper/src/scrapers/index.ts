import { ScrapeR<PERSON>ult } from '../../types/scrapper';
import { scraperIntegration } from '../utils/scraper/integration';

/**
 * WebPageScraper is now a wrapper around our new modular implementation
 * This maintains compatibility with existing code while using the new system
 */
class WebPageScraper {
	/**
	 * Initialize the scraper
	 */
	async initScraper() {
		// Initialize our new modular scraper through the integration layer
		await scraperIntegration.initScraper();
	}

	/**
	 * Scrape a URL using the new modular implementation
	 * @param url URL to scrape
	 * @returns ScrapeResult compatible with existing API
	 */
	async scrape(url: string): Promise<ScrapeResult> {
		// Use the integration layer to maintain compatibility
		return await scraperIntegration.scrape(url);
	}
}

// Create and initialize the singleton instance
const webPageScraper = new WebPageScraper();
await webPageScraper.initScraper();

export default webPageScraper;
