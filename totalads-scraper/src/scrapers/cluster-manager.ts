import * as fs from 'fs/promises';
import * as os from 'os';
import * as path from 'path';
import puppeteer from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';

import { MAX_CONCURRENCY } from '../config/constants';
import Cluster from '../utils/puppeteer-cluster/Cluster';

puppeteer.use(StealthPlugin());

export class ClusterManager {
	public cluster?: Cluster;
	private tempDir: string | null = null;

	async initialize() {
		try {
			console.log("Initialising cluster");

			// Create a unique temporary directory for this cluster instance
			const timestamp = Date.now();
			const randomId = Math.random().toString(36).substring(2, 11);
			const tempBaseDir = path.join(os.tmpdir(), "puppeteer-cluster");
			await fs.mkdir(tempBaseDir, { recursive: true });
			this.tempDir = path.join(
				tempBaseDir,
				`session-${timestamp}-${randomId}`,
			);
			await fs.mkdir(this.tempDir, { recursive: true });

			console.log(`Using temporary directory: ${this.tempDir}`);

			this.cluster = await Cluster.launch({
				concurrency: Cluster.CONCURRENCY_PAGE,
				maxConcurrency: MAX_CONCURRENCY,
				puppeteer,
				timeout: 0,
				puppeteerOptions: {
					timeout: 0,
					// Use the correct Chrome executable path based on the operating system
					executablePath:
						process.platform === "win32"
							? "C:\\Users\\<USER>\\.cache\\puppeteer\\chrome\\win64-121.0.6167.85\\chrome-win64\\chrome.exe"
							: process.platform === "darwin"
								? "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
								: "/usr/bin/chromium",
					headless: true,
					userDataDir: this.tempDir, // Use our custom temp directory
					args: [
						`--user-data-dir=${this.tempDir}`,
						"--no-zygote",
						"--no-sandbox",
						"--disable-setuid-sandbox",
						"--disable-dev-shm-usage",
						"--disable-gpu",
						"--disable-software-rasterizer",
						"--disable-features=site-per-process",
						"--no-first-run",
						"--disable-notifications",
						"--disable-popup-blocking",
						"--disable-web-security",
						"--disable-features=VizDisplayCompositor",
						"--disable-background-timer-throttling",
						"--disable-backgrounding-occluded-windows",
						"--disable-renderer-backgrounding",
						"--disable-field-trial-config",
						"--disable-ipc-flooding-protection",
						"--force-single-process-tabs",
					],
				},
			});

			console.log("Initialized cluster successfully");
			return this.cluster;
		} catch (error) {
			console.error("Error initializing cluster:", error);
			await this.cleanup();
			throw error;
		}
	}

	getCluster() {
		if (!this.cluster) {
			throw new Error(
				"Cluster not initialized. Call initialize() first.",
			);
		}
		return this.cluster;
	}

	/**
	 * Clean up resources including the cluster and temporary directories
	 */
	async cleanup(): Promise<void> {
		try {
			if (this.cluster) {
				await this.cluster.close();
				this.cluster = undefined;
			}

			// Clean up the temporary directory
			if (this.tempDir) {
				try {
					await fs.rm(this.tempDir, { recursive: true, force: true });
					console.log(
						`Cleaned up temporary directory: ${this.tempDir}`,
					);
				} catch (error) {
					console.error(
						"Error cleaning up temporary directory:",
						error,
					);
				} finally {
					this.tempDir = null;
				}
			}
		} catch (error) {
			console.error("Error during cleanup:", error);
		}
	}
}

const clusterManager = new ClusterManager();

// Handle process termination to ensure cleanup
const cleanup = async () => {
	console.log("Cleaning up cluster manager...");
	await clusterManager.cleanup();
	process.exit(0);
};

process.on("SIGINT", cleanup);
process.on("SIGTERM", cleanup);
process.on("exit", cleanup);

export default clusterManager;
