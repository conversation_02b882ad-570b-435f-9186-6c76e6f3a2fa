/* eslint-disable no-undef */
import { Page } from 'puppeteer';

import { CompanyInfo, ContactDetails, ScrapeResult } from '../../types/scrapper';
import { ELEMENTS_TO_REMOVE_FROM_FINAL_HTML } from '../config/constants';
import {
    extractAboutAndTeamInfo, findAboutAndTeamPages, mergeCompanyInfo
} from '../extractors/about-info';
import { getTitleAndDesc } from '../extractors/basic-info';
import {
    extractContactDetailsFromPage, findContactPages, isContactDetailsComplete, mergeContactDetails
} from '../extractors/contact-info';
import { getTablesAndReplaceWithPlaceholders } from '../utils/tables';
import {
    convertHTMLToText, replaceTablePlaceholdersWithAsciiTables
} from '../utils/text-processing';
import { normalizeURL } from '../utils/url';
import clusterManager from './cluster-manager';

export class PageScraper {
	private async removeUnnecessaryElements(page: Page) {
		console.log("Removing unnecessary elements for the page");
		const removedCount = await page.evaluate(
			(ELEMENTS_TO_REMOVE_FROM_FINAL_HTML) => {
				let removedCount = 0;

				document
					.querySelectorAll(
						ELEMENTS_TO_REMOVE_FROM_FINAL_HTML.join(", "),
					)
					.forEach((el) => {
						removedCount++;
						el.remove();
					});
				return removedCount;
			},
			ELEMENTS_TO_REMOVE_FROM_FINAL_HTML,
		);
		console.log(
			`Removed ${removedCount} unnecessary elements from the page`,
		);
	}

	private async getNestedLinks(page: Page) {
		console.log("Getting all nested links in the page");
		const nestedAnchorsHrefs = await page.evaluate(() => {
			const nestedAnchors = document.querySelectorAll("a");
			const nestedAnchorsHrefs: string[] = [];
			nestedAnchors.forEach((nestedAnchor) =>
				nestedAnchorsHrefs.push(nestedAnchor.href),
			);
			return nestedAnchorsHrefs;
		});
		const currentPageURL = new URL(normalizeURL(page.url()));
		const nestedLinks = new Set<string>();
		nestedAnchorsHrefs.forEach((href) => {
			let nestedAnchorHref = href;
			let nestedURL: URL;
			try {
				nestedURL = new URL(normalizeURL(href));
			} catch {
				nestedAnchorHref = `${currentPageURL.origin}${
					nestedAnchorHref.startsWith("/") ? "" : "/"
				}${nestedAnchorHref}`;
				try {
					nestedURL = new URL(normalizeURL(nestedAnchorHref));
				} catch {
					return;
				}
			}
			if (
				nestedURL.origin === currentPageURL.origin &&
				nestedURL.href !== currentPageURL.href
			)
				nestedLinks.add(nestedURL.href);
		});
		console.log(`Nested links: ${Array.from(nestedLinks)}`);
		return nestedLinks;
	}

	private async scrapeTask({
		page,
		url,
	}: {
		page: Page;
		url: string;
	}): Promise<{
		title: string | null;
		desc: null;
		nestedLinks: string[];
		text: string;
		contactDetails: ContactDetails;
		aboutData?: CompanyInfo;
	}> {
		console.log("Attach request listener to abort unwanted requests.");
		await page.setRequestInterception(true);

		page.on("request", (req) => {
			if (
				["image", "media", "font", "texttrack"].includes(
					req.resourceType(),
				)
			)
				req.abort();
			else req.continue();
		});

		console.log(`Navigating to page: ${url}`);
		await page.goto(url, {
			waitUntil: ["load", "domcontentloaded", "networkidle2"],
			timeout: 0,
		});
		console.log(`Navigated to page: ${url} successfully`);

		const { title, desc } = await getTitleAndDesc(page);
		const tables = await getTablesAndReplaceWithPlaceholders(page);
		const nestedLinks = Array.from(await this.getNestedLinks(page));
		await this.removeUnnecessaryElements(page);
		let text = await convertHTMLToText(page);
		text = replaceTablePlaceholdersWithAsciiTables(tables, text);

		// Extract contact details from the current page
		console.log("Extracting contact details from the current page");
		const contactDetails = await extractContactDetailsFromPage(page);
		console.log(`Scraped page: ${url} successfully`);

		return {
			title,
			desc,
			nestedLinks,
			text,
			contactDetails,
		};
	}

	async scrape(url: string): Promise<ScrapeResult> {
		const cluster = clusterManager.getCluster();

		const output = await cluster.execute(
			normalizeURL(url),
			async ({ page, data }) => {
				const basicOutput = await this.scrapeTask({ page, url: data });

				// If contact details are incomplete, try to find more info on other pages
				if (!isContactDetailsComplete(basicOutput.contactDetails)) {
					console.log(
						"Contact details incomplete, looking for contact/about pages",
					);
					const contactLinks = await findContactPages(page, data);

					// Visit each potential contact page until we find complete details
					for (const link of contactLinks) {
						if (
							isContactDetailsComplete(basicOutput.contactDetails)
						)
							break;

						console.log(
							`Navigating to potential contact page: ${link}`,
						);
						try {
							await page.goto(link, {
								waitUntil: [
									"load",
									"domcontentloaded",
									"networkidle2",
								],
								timeout: 30000,
							});

							const pageDetails =
								await extractContactDetailsFromPage(page);
							basicOutput.contactDetails = mergeContactDetails(
								basicOutput.contactDetails,
								pageDetails,
							);
						} catch (error) {
							console.error(
								`Error navigating to ${link}:`,
								error,
							);
						}
					}
				}

				console.log("Extracting about page information");
				// Find strictly about and team pages - limited to max 3 relevant pages
				const aboutLinks = await findAboutAndTeamPages(
					page,
					data,
					basicOutput.nestedLinks,
				);

				console.log(
					`Found ${aboutLinks.length} relevant about/team pages to process: ${aboutLinks.filter(Boolean).join(", ")}`,
				);

				// Helper function to normalize URLs for consistent comparison
				const normalizeAboutUrl = (url: string): string => {
					try {
						const urlObj = new URL(url);
						// Remove trailing slash for consistency
						return (
							urlObj.origin +
							urlObj.pathname.replace(/\/$/, "") +
							urlObj.search
						);
					} catch (e) {
						console.log("Error", e);
						return url; // Return original if invalid URL
					}
				};

				// Critical exclusion patterns that should never be processed as about/team pages
				// Expanded and aligned with patterns in findAboutAndTeamPages for consistency
				const criticalExcludePatterns = [
					"/contact",
					"/privacy",
					"/terms",
					"/book-a-call",
					"/free-website-analysis",
					"/free-quote",
					"/get-quote",
					"/book-demo",
					"/services",
					"/cart",
					"/blog",
					"/news",
					"/reviews",
					"/testimonials",
					"/case-studies",
					"/portfolio",
					"/shop",
					"/store",
					"/faq",
					"/support",
					"/help",
					"/join-us",
					"/login",
					"/register",
					"/signin",
					"/signup",
					"/schedule-",
					"/get-in-touch",
					"/request-",
				];

				// Additional deduplication to ensure we don't process the same URL twice
				// This catches any duplicates that might have slipped through findAboutAndTeamPages
				console.log(
					"Performing secondary URL deduplication and validation checks...",
				);
				const seenUrls = new Set<string>();
				const uniqueAboutLinks = aboutLinks.filter((link) => {
					if (!link) {
						console.log("Filtering out null/undefined link");
						return false;
					}

					// Check critical exclusion patterns first
					try {
						const urlObj = new URL(link);
						const path = urlObj.pathname.toLowerCase();

						// Check each exclusion pattern
						for (const pattern of criticalExcludePatterns) {
							if (path.includes(pattern)) {
								console.log(
									`Excluding URL: ${link} (contains excluded pattern: ${pattern})`,
								);
								return false;
							}
						}

						// Check for duplicates using normalized URL
						const normalizedUrl = normalizeAboutUrl(link);
						if (seenUrls.has(normalizedUrl)) {
							console.log(
								`Filtering out duplicate URL: ${link} (normalized: ${normalizedUrl})`,
							);
							return false;
						}

						// Valid and unique URL
						console.log(`Keeping unique about/team URL: ${link}`);
						seenUrls.add(normalizedUrl);
						return true;
					} catch (error) {
						console.error(`Error processing URL ${link}:`, error);
						return false;
					}
				});

				console.log(
					`After additional deduplication: ${uniqueAboutLinks.length} unique about/team pages to process`,
				);

				// Always process all found about pages (up to the limit of 3)
				// This ensures we get the most comprehensive information by visiting multiple pages
				const limitedAboutLinks = uniqueAboutLinks.slice(0, 3);

				// Initialize aboutData if not present
				if (!basicOutput.aboutData) {
					basicOutput.aboutData = {};
				}

				// Visit each potential about page and merge information
				for (let i = 0; i < limitedAboutLinks.length; i++) {
					const link = limitedAboutLinks[i];

					// Skip undefined or invalid links
					if (!link) {
						console.log(
							`Skipping invalid link at position ${i + 1}`,
						);
						continue;
					}

					console.log(
						`Navigating to about/team page ${i + 1}/${limitedAboutLinks.length}: ${link}`,
					);

					try {
						await page.goto(link, {
							waitUntil: [
								"load",
								"domcontentloaded",
								"networkidle2",
							],
							timeout: 30000,
						});

						// Extract about and team info from the current page
						const pageAboutData =
							await extractAboutAndTeamInfo(page);
						console.log(
							`Extracted data from page ${i + 1}: ${link}`,
							pageAboutData,
						);

						// Merge with existing data
						basicOutput.aboutData = mergeCompanyInfo(
							basicOutput.aboutData,
							pageAboutData,
						);
					} catch (error) {
						console.error(`Error navigating to ${link}:`, error);
					}
				}

				return {
					...basicOutput,
				};
			},
		);

		return output;
	}
}

export default new PageScraper();
