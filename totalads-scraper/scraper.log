{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T19:25:31.400Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:25:31.424Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:25:31.424Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T19:25:31.424Z"}
{"level":"info","message":"API request received to scrape URL: https://en.wikipedia.org/wiki/Web_scraping","timestamp":"2025-06-26T19:26:05.655Z"}
{"level":"info","message":"Starting scrape operation for URL: https://en.wikipedia.org/wiki/Web_scraping","timestamp":"2025-06-26T19:26:05.656Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-26T19:26:05.656Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-26T19:26:06.610Z"}
{"level":"info","message":"Executing task for URL: https://en.wikipedia.org/wiki/Web_scraping","timestamp":"2025-06-26T19:26:06.610Z"}
{"level":"info","message":"Navigation to https://en.wikipedia.org/wiki/Web_scraping successful","timestamp":"2025-06-26T19:26:10.011Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T19:26:10.012Z"}
{"level":"info","message":"Extracted metadata - Title: Web scraping - Wikipedia, Description: N/A","timestamp":"2025-06-26T19:26:10.014Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T19:26:10.014Z"}
{"level":"info","message":"Extracted 431 links from the page","timestamp":"2025-06-26T19:26:10.015Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T19:26:10.015Z"}
{"level":"info","message":"Converted 44843 characters of HTML to text","timestamp":"2025-06-26T19:26:10.042Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T19:26:10.043Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:26:10.053Z"}
{"level":"info","message":"Scrape completed for https://en.wikipedia.org/wiki/Web_scraping in 4407ms","timestamp":"2025-06-26T19:26:10.063Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T19:28:41.415Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T19:28:41.416Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T19:28:41.416Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T19:28:51.836Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T19:28:51.837Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T19:28:51.839Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T19:28:51.839Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T19:28:51.840Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T19:28:51.840Z"}
{"level":"info","message":"Converted 32257 characters of HTML to text","timestamp":"2025-06-26T19:28:51.873Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T19:28:51.873Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:28:51.877Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 10473ms","timestamp":"2025-06-26T19:28:51.889Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T19:37:09.087Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:37:09.120Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:37:09.121Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T19:37:09.121Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T19:39:31.013Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:39:31.037Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:39:31.037Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T19:39:31.037Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T19:39:33.343Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T19:39:33.344Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-26T19:39:33.345Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-26T19:39:34.778Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T19:39:34.779Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T19:39:41.622Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T19:39:41.623Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T19:39:41.628Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T19:39:41.628Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T19:39:41.634Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T19:39:41.635Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-26T19:39:41.660Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T19:39:41.660Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:39:41.670Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-26T19:39:41.671Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T19:39:41.683Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-26T19:39:41.687Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 10184ms","timestamp":"2025-06-26T19:39:43.528Z"}
{"level":"info","message":"API request received to scrape URL: https://hyperlinq.in/","timestamp":"2025-06-26T19:40:09.867Z"}
{"level":"info","message":"Starting scrape operation for URL: https://hyperlinq.in/","timestamp":"2025-06-26T19:40:09.867Z"}
{"level":"info","message":"Executing task for URL: https://hyperlinq.in","timestamp":"2025-06-26T19:40:09.867Z"}
{"level":"info","message":"Navigation to https://hyperlinq.in successful","timestamp":"2025-06-26T19:40:13.067Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T19:40:13.067Z"}
{"level":"info","message":"Extracted metadata - Title: Hyperlinq Technology | Marketi..., Description: Transform your marketing strat...","timestamp":"2025-06-26T19:40:13.068Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T19:40:13.068Z"}
{"level":"info","message":"Extracted 60 links from the page","timestamp":"2025-06-26T19:40:13.069Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T19:40:13.069Z"}
{"level":"info","message":"Converted 7182 characters of HTML to text","timestamp":"2025-06-26T19:40:13.082Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T19:40:13.083Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:40:13.085Z"}
{"level":"info","message":"Extracting about data from: https://hyperlinq.in","timestamp":"2025-06-26T19:40:13.086Z"}
{"level":"info","message":"Finding about and team pages from: https://hyperlinq.in","timestamp":"2025-06-26T19:40:13.091Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-26T19:40:13.092Z"}
{"level":"info","message":"Scrape completed for https://hyperlinq.in/ in 5706ms","timestamp":"2025-06-26T19:40:15.573Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com","timestamp":"2025-06-26T19:40:42.494Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com","timestamp":"2025-06-26T19:40:42.494Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T19:40:42.494Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T19:40:47.894Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T19:40:47.899Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T19:40:47.904Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T19:40:47.905Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T19:40:47.906Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T19:40:47.907Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-26T19:40:47.925Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T19:40:47.925Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:40:47.929Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-26T19:40:47.931Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T19:40:47.944Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-26T19:40:47.949Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com in 7148ms","timestamp":"2025-06-26T19:40:49.642Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T19:59:22.668Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:59:22.693Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T19:59:22.694Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T19:59:22.694Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:05:46.576Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:05:46.600Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:05:46.600Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:05:46.600Z"}
{"level":"info","message":"API request received to scrape URL: https://hyperlinq.in/","timestamp":"2025-06-26T20:05:51.110Z"}
{"level":"info","message":"Starting scrape operation for URL: https://hyperlinq.in/","timestamp":"2025-06-26T20:05:51.110Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-26T20:05:51.111Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-26T20:05:51.939Z"}
{"level":"info","message":"Executing task for URL: https://hyperlinq.in","timestamp":"2025-06-26T20:05:51.940Z"}
{"level":"info","message":"Navigation to https://hyperlinq.in successful","timestamp":"2025-06-26T20:05:55.825Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T20:05:55.825Z"}
{"level":"info","message":"Extracted metadata - Title: Hyperlinq Technology | Marketi..., Description: Transform your marketing strat...","timestamp":"2025-06-26T20:05:55.828Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T20:05:55.828Z"}
{"level":"info","message":"Extracted 60 links from the page","timestamp":"2025-06-26T20:05:55.828Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T20:05:55.828Z"}
{"level":"info","message":"Converted 7182 characters of HTML to text","timestamp":"2025-06-26T20:05:55.848Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T20:05:55.848Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:05:55.865Z"}
{"level":"info","message":"Extracting about data from: https://hyperlinq.in","timestamp":"2025-06-26T20:05:55.866Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-26T20:05:55.867Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:05:55.869Z"}
{"level":"info","message":"Finding about and team pages from: https://hyperlinq.in","timestamp":"2025-06-26T20:05:55.869Z"}
{"level":"info","message":"Finding about and team pages from: https://hyperlinq.in","timestamp":"2025-06-26T20:05:55.869Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:05:55.871Z"}
{"level":"info","message":"Scrape completed for https://hyperlinq.in/ in 4774ms","timestamp":"2025-06-26T20:05:55.884Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:06:10.485Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:06:10.485Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T20:06:10.486Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:06:29.734Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:06:29.757Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:06:29.758Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:06:29.758Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:06:40.922Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:06:40.923Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-26T20:06:40.923Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-26T20:06:41.788Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T20:06:41.788Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T20:06:44.758Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T20:06:44.758Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T20:06:44.759Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T20:06:44.759Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T20:06:44.760Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T20:06:44.760Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-26T20:06:44.784Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T20:06:44.784Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:06:44.804Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-26T20:06:44.805Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-26T20:06:44.805Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:06:44.810Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:06:44.810Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:06:44.811Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:06:44.812Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 3901ms","timestamp":"2025-06-26T20:06:44.824Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:07:01.160Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:07:01.182Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:07:01.182Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:07:01.182Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:07:45.861Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:07:45.901Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:07:45.901Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:07:45.902Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:08:06.846Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:08:06.868Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:08:06.868Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:08:06.868Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-26T20:08:46.971Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:08:46.998Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-26T20:08:46.998Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-26T20:08:46.998Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:08:56.261Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:08:56.262Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-26T20:08:56.263Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-26T20:08:57.171Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T20:08:57.171Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T20:09:01.375Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T20:09:01.378Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T20:09:01.381Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T20:09:01.383Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T20:09:01.385Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T20:09:01.385Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-26T20:09:01.417Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T20:09:01.418Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:09:01.443Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-26T20:09:01.444Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-26T20:09:01.444Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:09:01.449Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:09:01.450Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:09:01.450Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:09:01.452Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5208ms","timestamp":"2025-06-26T20:09:01.470Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:10:23.438Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-26T20:10:23.440Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-26T20:10:23.441Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-26T20:10:28.575Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-26T20:10:28.575Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-26T20:10:28.576Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-26T20:10:28.576Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-26T20:10:28.577Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-26T20:10:28.577Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-26T20:10:28.623Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-26T20:10:28.624Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:10:28.628Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-26T20:10:28.628Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-26T20:10:28.629Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:10:28.630Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:10:28.631Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-26T20:10:28.631Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:10:28.632Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5202ms","timestamp":"2025-06-26T20:10:28.643Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T08:19:30.320Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:19:30.351Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:19:30.351Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T08:19:30.351Z"}
{"level":"info","message":"API request received to scrape URL: https://www.example.com","timestamp":"2025-06-28T08:20:15.835Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.example.com","timestamp":"2025-06-28T08:20:15.836Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T08:20:15.836Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T08:20:17.293Z"}
{"level":"info","message":"Executing task for URL: https://www.example.com","timestamp":"2025-06-28T08:20:17.293Z"}
{"level":"info","message":"Navigation to https://www.example.com successful","timestamp":"2025-06-28T08:20:19.958Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:20:19.958Z"}
{"level":"info","message":"Extracted metadata - Title: Example Domain, Description: N/A","timestamp":"2025-06-28T08:20:19.965Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:20:19.966Z"}
{"level":"info","message":"Extracted 1 links from the page","timestamp":"2025-06-28T08:20:19.967Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:20:19.967Z"}
{"level":"info","message":"Converted 228 characters of HTML to text","timestamp":"2025-06-28T08:20:19.978Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:20:19.978Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:20:19.996Z"}
{"level":"info","message":"Extracting about data from: https://www.example.com","timestamp":"2025-06-28T08:20:19.997Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:20:19.997Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:19.999Z"}
{"level":"info","message":"Finding about and team pages from: https://www.example.com","timestamp":"2025-06-28T08:20:19.999Z"}
{"level":"info","message":"Finding about and team pages from: https://www.example.com","timestamp":"2025-06-28T08:20:19.999Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:20.007Z"}
{"level":"info","message":"Scrape completed for https://www.example.com in 4182ms","timestamp":"2025-06-28T08:20:20.018Z"}
{"level":"info","message":"API request received to scrape URL: https://html.com/tables/rowspan-colspan/","timestamp":"2025-06-28T08:20:35.354Z"}
{"level":"info","message":"Starting scrape operation for URL: https://html.com/tables/rowspan-colspan/","timestamp":"2025-06-28T08:20:35.354Z"}
{"level":"info","message":"Executing task for URL: https://html.com/tables/rowspan-colspan","timestamp":"2025-06-28T08:20:35.354Z"}
{"level":"info","message":"Navigation to https://html.com/tables/rowspan-colspan successful","timestamp":"2025-06-28T08:20:40.652Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:20:40.652Z"}
{"level":"info","message":"Extracted metadata - Title: Just a moment..., Description: N/A","timestamp":"2025-06-28T08:20:40.654Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:20:40.654Z"}
{"level":"info","message":"Extracted 1 links from the page","timestamp":"2025-06-28T08:20:40.655Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:20:40.655Z"}
{"level":"info","message":"Converted 370 characters of HTML to text","timestamp":"2025-06-28T08:20:40.667Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:20:40.667Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:20:40.669Z"}
{"level":"info","message":"Extracting about data from: https://html.com/tables/rowspan-colspan","timestamp":"2025-06-28T08:20:40.670Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:20:40.670Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:40.671Z"}
{"level":"info","message":"Finding about and team pages from: https://html.com/tables/rowspan-colspan","timestamp":"2025-06-28T08:20:40.671Z"}
{"level":"info","message":"Finding about and team pages from: https://html.com/tables/rowspan-colspan","timestamp":"2025-06-28T08:20:40.671Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:40.672Z"}
{"level":"info","message":"Scrape completed for https://html.com/tables/rowspan-colspan/ in 5326ms","timestamp":"2025-06-28T08:20:40.680Z"}
{"level":"info","message":"API request received to scrape URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:21:19.509Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:21:19.509Z"}
{"level":"info","message":"Executing task for URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:21:19.509Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","stack":"ScraperError: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:22:7)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:137:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:79:22)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T08:21:49.711Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","stack":"ScraperError: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:22:7)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:137:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:79:22)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T08:21:49.711Z"}
{"level":"error","message":"Error in scrape controller: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","timestamp":"2025-06-28T08:21:49.711Z"}
{"level":"info","message":"API request received to scrape URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:02.906Z"}
{"level":"info","message":"Starting scrape operation for URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:02.906Z"}
{"level":"info","message":"Executing task for URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:02.906Z"}
{"level":"info","message":"Navigation to https://en.wikipedia.org/wiki/Comparison_of_programming_languages successful","timestamp":"2025-06-28T08:22:05.059Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:22:05.059Z"}
{"level":"info","message":"Extracted metadata - Title: Comparison of programming lang..., Description: N/A","timestamp":"2025-06-28T08:22:05.061Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:22:05.061Z"}
{"level":"info","message":"Extracted 804 links from the page","timestamp":"2025-06-28T08:22:05.066Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:22:05.066Z"}
{"level":"info","message":"Converted 55426 characters of HTML to text","timestamp":"2025-06-28T08:22:05.121Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:22:05.121Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:22:05.124Z"}
{"level":"info","message":"Extracting about data from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:05.125Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:22:05.125Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:05.127Z"}
{"level":"info","message":"Finding about and team pages from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:05.127Z"}
{"level":"info","message":"Finding about and team pages from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:22:05.127Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:05.128Z"}
{"level":"info","message":"Scrape completed for https://en.wikipedia.org/wiki/Comparison_of_programming_languages in 2233ms","timestamp":"2025-06-28T08:22:05.139Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:22:25.576Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:22:25.576Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T08:22:25.577Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T08:22:32.196Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:22:32.199Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T08:22:32.202Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:22:32.203Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T08:22:32.205Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:22:32.206Z"}
{"level":"info","message":"Converted 32257 characters of HTML to text","timestamp":"2025-06-28T08:22:32.293Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:22:32.295Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:22:32.303Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T08:22:32.304Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:22:32.305Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:32.308Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:22:32.308Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:22:32.309Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:32.311Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6753ms","timestamp":"2025-06-28T08:22:32.329Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T08:31:28.518Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:31:28.549Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:31:28.549Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T08:31:28.549Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:31:33.994Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:31:33.994Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T08:31:33.995Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T08:31:35.180Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T08:31:35.180Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T08:31:38.581Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:31:38.581Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T08:31:38.583Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:31:38.583Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T08:31:38.584Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:31:38.584Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T08:31:38.621Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:31:38.621Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:31:38.636Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T08:31:38.637Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:31:38.637Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:31:38.642Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:31:38.642Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:31:38.643Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:31:38.645Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 4669ms","timestamp":"2025-06-28T08:31:38.663Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T08:33:12.439Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:33:12.465Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:33:12.466Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T08:33:12.466Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:34:23.371Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:34:23.371Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T08:34:23.372Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T08:34:24.302Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T08:34:24.302Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T08:34:28.234Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:34:28.244Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T08:34:28.250Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:34:28.250Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T08:34:28.252Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:34:28.252Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T08:34:28.281Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:34:28.282Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:34:28.313Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T08:34:28.315Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:34:28.316Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:34:28.324Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:34:28.324Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:34:28.325Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T08:34:28.327Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T08:34:28.328Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T08:34:28.328Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T08:34:28.328Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T08:34:29.876Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:34:29.877Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:34:29.882Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6526ms","timestamp":"2025-06-28T08:34:29.897Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T08:38:31.363Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:38:31.390Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:38:31.391Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T08:38:31.391Z"}
{"level":"info","message":"API request received to scrape URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:09.604Z"}
{"level":"info","message":"Starting scrape operation for URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:09.604Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T08:39:09.605Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T08:39:10.467Z"}
{"level":"info","message":"Executing task for URL: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:10.467Z"}
{"level":"info","message":"Navigation to https://en.wikipedia.org/wiki/Comparison_of_programming_languages successful","timestamp":"2025-06-28T08:39:12.683Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:39:12.684Z"}
{"level":"info","message":"Extracted metadata - Title: Comparison of programming lang..., Description: N/A","timestamp":"2025-06-28T08:39:12.686Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:39:12.686Z"}
{"level":"info","message":"Extracted 804 links from the page","timestamp":"2025-06-28T08:39:12.690Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:39:12.690Z"}
{"level":"info","message":"Converted 55426 characters of HTML to text","timestamp":"2025-06-28T08:39:12.733Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:39:12.733Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:39:12.752Z"}
{"level":"info","message":"Extracting about data from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:12.754Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:39:12.754Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:39:12.758Z"}
{"level":"info","message":"Finding about and team pages from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:12.759Z"}
{"level":"info","message":"Finding about and team pages from: https://en.wikipedia.org/wiki/Comparison_of_programming_languages","timestamp":"2025-06-28T08:39:12.759Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T08:39:12.760Z"}
{"level":"info","message":"Found 0 unique about/team pages from nested links","timestamp":"2025-06-28T08:39:12.761Z"}
{"level":"info","message":"Scrape completed for https://en.wikipedia.org/wiki/Comparison_of_programming_languages in 3167ms","timestamp":"2025-06-28T08:39:12.771Z"}
{"level":"info","message":"API request received to scrape URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:39:52.389Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:39:52.389Z"}
{"level":"info","message":"Executing task for URL: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:39:52.390Z"}
{"level":"info","message":"Navigation to https://www.w3schools.com/html/html_tables.asp successful","timestamp":"2025-06-28T08:40:10.691Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:40:10.692Z"}
{"level":"info","message":"Extracted metadata - Title: W3Schools.com, Description: W3Schools offers free online t...","timestamp":"2025-06-28T08:40:10.693Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:40:10.693Z"}
{"level":"info","message":"Extracted 774 links from the page","timestamp":"2025-06-28T08:40:10.695Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:40:10.695Z"}
{"level":"info","message":"Converted 44757 characters of HTML to text","timestamp":"2025-06-28T08:40:10.720Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:40:10.720Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:40:10.722Z"}
{"level":"info","message":"Extracting about data from: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:40:10.723Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:40:10.723Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:40:10.724Z"}
{"level":"info","message":"Finding about and team pages from: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:40:10.725Z"}
{"level":"info","message":"Finding about and team pages from: https://www.w3schools.com/html/html_tables.asp","timestamp":"2025-06-28T08:40:10.725Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T08:40:10.725Z"}
{"level":"info","message":"Found 3 unique about/team pages from nested links","timestamp":"2025-06-28T08:40:10.726Z"}
{"level":"info","message":"Found 3 about/team pages to extract","timestamp":"2025-06-28T08:40:10.726Z"}
{"level":"info","message":"Extracting company info from 3 pages","timestamp":"2025-06-28T08:40:10.726Z"}
{"level":"info","message":"Extracting about info from: https://www.w3schools.com/about/default.asp","timestamp":"2025-06-28T08:40:14.789Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:40:14.789Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:40:14.792Z"}
{"level":"info","message":"Extracting about info from: https://www.w3schools.com/about/about_copyright.asp","timestamp":"2025-06-28T08:40:18.667Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:40:18.667Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:40:18.678Z"}
{"level":"info","message":"Scrape completed for https://www.w3schools.com/html/html_tables.asp in 26353ms","timestamp":"2025-06-28T08:40:18.742Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T08:42:10.055Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:42:10.086Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T08:42:10.086Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T08:42:10.086Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:43:29.746Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T08:43:29.747Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T08:43:29.748Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T08:43:30.639Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T08:43:30.639Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T08:43:34.629Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T08:43:34.629Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T08:43:34.630Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T08:43:34.631Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T08:43:34.632Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T08:43:34.632Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T08:43:34.660Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T08:43:34.660Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:43:34.681Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T08:43:34.681Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:43:34.682Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:43:34.686Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:43:34.686Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T08:43:34.687Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T08:43:34.688Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T08:43:34.689Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T08:43:34.689Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T08:43:34.689Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T08:43:36.029Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T08:43:36.037Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:43:36.042Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6317ms","timestamp":"2025-06-28T08:43:36.064Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T12:56:35.768Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T12:56:35.797Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T12:56:35.798Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T12:56:35.798Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T13:01:24.385Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T13:01:24.385Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T13:01:24.386Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T13:01:25.375Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T13:01:25.376Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T13:01:32.359Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T13:01:32.367Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T13:01:32.372Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T13:01:32.374Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T13:01:32.376Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T13:01:32.376Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T13:01:32.402Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T13:01:32.402Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T13:01:32.426Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T13:01:32.426Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T13:01:32.427Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T13:01:32.431Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T13:01:32.431Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T13:01:32.432Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T13:01:32.433Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T13:01:32.434Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T13:01:32.434Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T13:01:32.434Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T13:01:33.816Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T13:01:33.817Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T13:01:33.842Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 9485ms","timestamp":"2025-06-28T13:01:33.870Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:12:13.835Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:12:13.849Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:12:13.850Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:12:13.850Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:13:11.055Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:13:11.096Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:13:11.096Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:13:11.096Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:26:32.709Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:26:32.853Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:26:32.854Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:26:32.854Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:31:18.179Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:31:18.220Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:31:18.220Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:31:18.220Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:32:01.880Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:01.920Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:01.920Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:32:01.920Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:32:23.612Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:23.655Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:23.655Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:32:23.655Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:32:38.289Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:38.334Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:38.335Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:32:38.335Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:32:47.556Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:47.601Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:32:47.601Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:32:47.601Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:33:06.255Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:33:06.268Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:33:06.268Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:33:06.268Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T13:33:12.971Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T13:33:12.971Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T13:33:12.972Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T13:33:14.078Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T13:33:14.079Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T13:33:19.343Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T13:33:19.344Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T13:33:19.380Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T13:33:19.380Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T13:33:19.380Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T13:33:19.381Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T13:33:19.406Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T13:33:19.407Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T13:33:19.420Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T13:33:19.421Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T13:33:19.422Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T13:33:19.425Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T13:33:19.426Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T13:33:19.426Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T13:33:19.429Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T13:33:19.430Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T13:33:19.430Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T13:33:19.430Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T13:33:21.247Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T13:33:21.247Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T13:33:21.248Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 8292ms","timestamp":"2025-06-28T13:33:21.263Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:36:27.091Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:36:27.103Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:36:27.104Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:36:27.104Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:39:59.284Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:39:59.340Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:39:59.340Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:39:59.340Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:40:36.883Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:40:36.933Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:40:36.934Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:40:36.934Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:40:42.158Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:40:42.195Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:40:42.196Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:40:42.196Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:41:09.963Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:41:10.000Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:41:10.001Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:41:10.001Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:44:15.740Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:44:15.776Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:44:15.776Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:44:15.777Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T13:44:37.386Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:44:37.419Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T13:44:37.420Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T13:44:37.420Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:07:55.950Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:07:55.993Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:07:55.994Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:07:55.994Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:08:31.540Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:08:31.541Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:08:31.542Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:08:32.486Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:08:32.486Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:08:36.626Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:08:36.627Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:08:36.643Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:08:36.644Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:08:36.646Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:08:36.647Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:08:36.678Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:08:36.678Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:08:36.699Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:08:36.700Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:08:36.700Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:08:36.705Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:08:36.705Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:08:36.706Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:08:36.708Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:08:36.709Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:08:36.709Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:08:36.709Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:08:37.810Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:08:37.810Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:08:37.820Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6296ms","timestamp":"2025-06-28T14:08:37.837Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:11:58.849Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:11:58.861Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:11:58.861Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:11:58.861Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:16:06.280Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:16:06.280Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:16:06.281Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:16:07.176Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:16:07.176Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:16:10.291Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:16:10.292Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:16:10.300Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:16:10.300Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:16:10.304Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:16:10.305Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:16:10.335Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:16:10.335Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:16:10.357Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:16:10.358Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:16:10.358Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:16:10.361Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:16:10.362Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:16:10.362Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:16:10.364Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:16:10.364Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:16:10.364Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:16:10.364Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:16:11.507Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:16:11.507Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:16:11.519Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5253ms","timestamp":"2025-06-28T14:16:11.533Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:23:57.754Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:23:57.757Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:23:57.758Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:24:38.605Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:24:38.630Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:24:38.630Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:24:38.630Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:24:43.398Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:24:43.398Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:24:43.398Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:24:44.252Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:24:44.253Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:24:48.666Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:24:48.667Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:24:48.672Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:24:48.674Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:24:48.676Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:24:48.677Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:24:48.709Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:24:48.709Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:24:48.720Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:24:48.721Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:24:48.721Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:24:48.723Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:24:48.723Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:24:48.723Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:24:48.725Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:24:48.725Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:24:48.725Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:24:48.725Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:24:50.024Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:24:50.024Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:24:50.028Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6652ms","timestamp":"2025-06-28T14:24:50.050Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:30:36.867Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:30:36.881Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:30:36.881Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:30:36.882Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:30:50.511Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:30:50.550Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:30:50.555Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:30:50.555Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:31:05.375Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:05.416Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:05.416Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:31:05.417Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:31:20.392Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:20.425Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:20.426Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:31:20.426Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:31:35.119Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:35.153Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:31:35.154Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:31:35.154Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:31:47.554Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:31:47.554Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:31:47.555Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:31:48.444Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:31:48.444Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:31:54.923Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:31:54.923Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:31:54.929Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:31:54.930Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:31:54.938Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:31:54.938Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:31:54.976Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:31:54.977Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:31:55.001Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:31:55.001Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:31:55.002Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:31:55.005Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:31:55.006Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:31:55.006Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:31:55.007Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:31:55.008Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:31:55.008Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:31:55.008Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:31:56.262Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:31:56.262Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:31:56.264Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 8758ms","timestamp":"2025-06-28T14:31:56.312Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:32:55.834Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:32:55.835Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:32:55.836Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:33:01.710Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:33:01.711Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:33:01.750Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:33:01.750Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:33:01.751Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:33:01.751Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:33:01.775Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:33:01.776Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:33:01.777Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:33:01.778Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:33:01.778Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:33:01.779Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:33:01.779Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:33:01.779Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:33:01.780Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:33:01.780Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:33:01.780Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:33:01.780Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:33:03.155Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:33:03.157Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:33:03.161Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 7343ms","timestamp":"2025-06-28T14:33:03.178Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:35:04.572Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:35:04.607Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:35:04.608Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:35:04.608Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:35:51.404Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:35:51.446Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:35:51.447Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:35:51.447Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:36:19.081Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:36:19.128Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:36:19.131Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:36:19.131Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:36:28.956Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:36:28.957Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:36:28.958Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:36:29.841Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:36:29.841Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:36:33.520Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:36:33.521Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:36:33.525Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:36:33.526Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:36:33.531Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:36:33.531Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:36:33.561Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:36:33.562Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T14:36:33.563Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:36:33.564Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:36:33.565Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:36:33.581Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:36:33.581Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:36:33.581Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:36:33.585Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:36:33.585Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:36:33.585Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:36:33.585Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:36:35.182Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:36:35.183Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:36:35.187Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 6249ms","timestamp":"2025-06-28T14:36:35.206Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:38:23.940Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:38:23.954Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:38:23.954Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:38:23.954Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:38:27.607Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:38:27.608Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:38:27.611Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:38:28.205Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:38:28.206Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:38:34.348Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:38:34.349Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:38:34.352Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:38:34.352Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:38:34.353Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:38:34.354Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:38:34.398Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:38:34.398Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T14:38:34.399Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:38:34.400Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:38:34.400Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:38:34.425Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:38:34.426Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:38:34.426Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:38:34.428Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:38:34.429Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:38:34.429Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:38:34.429Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:38:36.017Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:38:36.018Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1188:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:38:36.021Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 8428ms","timestamp":"2025-06-28T14:38:36.036Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:40:25.242Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:40:25.254Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:40:25.254Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:40:25.254Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:41:06.894Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:41:06.934Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:41:06.934Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:41:06.934Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:41:27.855Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:41:27.893Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:41:27.894Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:41:27.894Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:42:03.712Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:42:03.815Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:42:03.817Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:42:03.817Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:42:25.986Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:42:25.986Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:42:25.987Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:42:26.912Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:42:26.913Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:42:30.191Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:42:30.191Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:42:30.192Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:42:30.192Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:42:30.194Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:42:30.194Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:42:30.221Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:42:30.222Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T14:42:30.222Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:42:30.223Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:42:30.223Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:42:30.233Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:42:30.234Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:42:30.234Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:42:30.235Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:42:30.236Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:42:30.236Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:42:30.236Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:42:31.483Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:42:31.483Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:42:31.486Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5519ms","timestamp":"2025-06-28T14:42:31.505Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:45:49.400Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:45:49.414Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:45:49.414Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:45:49.414Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:46:00.894Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:00.930Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:00.930Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:46:00.930Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:46:13.503Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:13.537Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:13.538Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:46:13.539Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:46:34.851Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:34.890Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:34.890Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:46:34.891Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:46:50.500Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:50.612Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:46:50.613Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:46:50.613Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:47:01.325Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:47:01.326Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:47:01.327Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:47:02.275Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:47:02.275Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:47:05.766Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:47:05.767Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:47:05.769Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:47:05.770Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:47:05.775Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:47:05.776Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:47:05.801Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:47:05.801Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T14:47:05.802Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:47:05.803Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:47:05.803Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:47:05.815Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:47:05.815Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:47:05.815Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:47:05.817Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:47:05.818Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:47:05.818Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:47:05.818Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:47:06.922Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:47:06.922Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:47:06.924Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5614ms","timestamp":"2025-06-28T14:47:06.940Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T14:50:06.843Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:50:06.867Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T14:50:06.867Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T14:50:06.867Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:57:21.327Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T14:57:21.327Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T14:57:21.328Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T14:57:22.206Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T14:57:22.206Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T14:57:25.898Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T14:57:25.899Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T14:57:25.901Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T14:57:25.901Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T14:57:25.902Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T14:57:25.903Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T14:57:25.927Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T14:57:25.927Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T14:57:25.929Z"}
{"level":"info","message":"Extracting about data from: https://www.ezrankings.com","timestamp":"2025-06-28T14:57:25.931Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:57:25.931Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:57:25.956Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:57:25.956Z"}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T14:57:25.956Z"}
{"level":"info","message":"No about pages found in DOM, falling back to nested links...","timestamp":"2025-06-28T14:57:25.959Z"}
{"level":"info","message":"Found 1 unique about/team pages from nested links","timestamp":"2025-06-28T14:57:25.960Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-28T14:57:25.960Z"}
{"level":"info","message":"Extracting company info from 1 pages","timestamp":"2025-06-28T14:57:25.960Z"}
{"level":"info","message":"Extracting about info from: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T14:57:27.166Z"}
{"level":"info","message":"Extracting about page information","timestamp":"2025-06-28T14:57:27.167Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:57:27.186Z"}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 5891ms","timestamp":"2025-06-28T14:57:27.218Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:47:38.395Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T15:47:38.505Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:47:38.517Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T15:47:38.517Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:48:20.919Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T15:48:21.236Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:48:21.294Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T15:48:21.294Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:48:58.023Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T15:48:58.270Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:48:58.319Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T15:48:58.319Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:51:48.150Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T15:51:48.446Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:51:48.536Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T15:51:48.537Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:52:12.055Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T15:52:12.327Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:52:12.358Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T15:52:12.359Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:52:37.315Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T15:52:37.585Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T15:52:37.615Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T15:52:37.615Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T15:55:24.151Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T15:55:24.151Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T15:55:24.152Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T15:55:25.105Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T15:55:25.105Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T15:55:29.489Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T15:55:29.490Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T15:55:29.501Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T15:55:29.502Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T15:55:29.506Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T15:55:29.507Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T15:55:29.538Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T15:55:29.538Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T15:55:29.540Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.ezrankings.com","timestamp":"2025-06-28T15:55:29.541Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com","timestamp":"2025-06-28T15:55:29.541Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T15:55:29.541Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T15:55:29.541Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":0,"timestamp":"2025-06-28T15:55:29.544Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T15:55:29.544Z"}
{"level":"info","message":"Finding about and team pages for https://www.ezrankings.com","timestamp":"2025-06-28T15:55:29.544Z"}
{"level":"info","message":"Found 14 potential about/team pages","timestamp":"2025-06-28T15:55:29.545Z"}
{"level":"info","message":"Found 14 about/team pages to extract","timestamp":"2025-06-28T15:55:29.545Z"}
{"level":"info","message":"Extracting enhanced about data from 14 pages","timestamp":"2025-06-28T15:55:29.545Z"}
{"level":"info","message":"Visiting page: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T15:55:29.545Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T15:55:30.863Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T15:55:30.868Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T15:55:30.869Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":3,"timestamp":"2025-06-28T15:55:30.872Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.ezrankings.com/seo-company-india.html","timestamp":"2025-06-28T15:55:30.873Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com/seo-company-india.html","timestamp":"2025-06-28T15:55:32.272Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T15:55:32.273Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T15:55:32.278Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":0,"timestamp":"2025-06-28T15:55:32.282Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 8155ms","timestamp":"2025-06-28T15:55:32.306Z"}
{"level":"info","message":"API request received to scrape URL: https://hyperlinq.in/","timestamp":"2025-06-28T16:00:28.174Z"}
{"level":"info","message":"Starting scrape operation for URL: https://hyperlinq.in/","timestamp":"2025-06-28T16:00:28.175Z"}
{"level":"info","message":"Executing task for URL: https://hyperlinq.in","timestamp":"2025-06-28T16:00:28.176Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:01:37.596Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T16:01:37.778Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:01:37.800Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:01:37.801Z"}
{"level":"info","message":"API request received to scrape URL: https://hyperlinq.in/","timestamp":"2025-06-28T16:01:40.091Z"}
{"level":"info","message":"Starting scrape operation for URL: https://hyperlinq.in/","timestamp":"2025-06-28T16:01:40.092Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T16:01:40.093Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 5","timestamp":"2025-06-28T16:01:40.576Z"}
{"level":"info","message":"Executing task for URL: https://hyperlinq.in","timestamp":"2025-06-28T16:01:40.577Z"}
{"level":"info","message":"Navigation to https://hyperlinq.in successful","timestamp":"2025-06-28T16:01:47.302Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T16:01:47.302Z"}
{"level":"info","message":"Extracted metadata - Title: Hyperlinq Technology | Marketi..., Description: Transform your marketing strat...","timestamp":"2025-06-28T16:01:47.303Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T16:01:47.303Z"}
{"level":"info","message":"Extracted 60 links from the page","timestamp":"2025-06-28T16:01:47.303Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T16:01:47.303Z"}
{"level":"info","message":"Converted 7213 characters of HTML to text","timestamp":"2025-06-28T16:01:47.322Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T16:01:47.322Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T16:01:47.323Z"}
{"level":"info","message":"Extracting enhanced about data from: https://hyperlinq.in","timestamp":"2025-06-28T16:01:47.324Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://hyperlinq.in","timestamp":"2025-06-28T16:01:47.324Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:01:47.324Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:01:47.324Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-28T16:01:47.326Z","valuesCount":6}
{"level":"info","message":"Finding about and team pages from: https://hyperlinq.in","timestamp":"2025-06-28T16:01:47.326Z"}
{"level":"info","message":"Finding about and team pages for https://hyperlinq.in","timestamp":"2025-06-28T16:01:47.326Z"}
{"level":"info","message":"Found 2 potential about/team pages","timestamp":"2025-06-28T16:01:47.326Z"}
{"level":"info","message":"Found 2 about/team pages to extract","timestamp":"2025-06-28T16:01:47.326Z"}
{"level":"info","message":"Extracting enhanced about data from 2 pages","timestamp":"2025-06-28T16:01:47.326Z"}
{"level":"info","message":"Visiting page: https://hyperlinq.in/about","timestamp":"2025-06-28T16:01:47.326Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://hyperlinq.in/about","timestamp":"2025-06-28T16:01:49.969Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:01:49.969Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:01:49.969Z"}
{"globalPresence":false,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-28T16:01:49.970Z","valuesCount":6}
{"level":"info","message":"Visiting page: https://www.linkedin.com/company/hyperlinq-technology/","timestamp":"2025-06-28T16:01:49.970Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.linkedin.com/company/hyperlinq-technology/","timestamp":"2025-06-28T16:01:53.639Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:01:53.640Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:01:53.641Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T16:01:53.644Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://hyperlinq.in/ in 13591ms","timestamp":"2025-06-28T16:01:53.683Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:03:43.157Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 60000ms\n  - Max concurrency: 5","timestamp":"2025-06-28T16:03:43.264Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:03:43.276Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:03:43.276Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:04:57.609Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T16:04:57.911Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:04:57.958Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:04:57.959Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:05:26.380Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T16:05:26.657Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:05:26.687Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:05:26.688Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:20:12.265Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T16:20:12.452Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:20:12.475Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:20:12.475Z"}
{"level":"info","message":"API request received to scrape URL: https://hyperlinq.in/","timestamp":"2025-06-28T16:20:24.401Z"}
{"level":"info","message":"Starting scrape operation for URL: https://hyperlinq.in/","timestamp":"2025-06-28T16:20:24.402Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T16:20:24.403Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-28T16:20:25.424Z"}
{"level":"info","message":"Executing task for URL: https://hyperlinq.in","timestamp":"2025-06-28T16:20:25.424Z"}
{"level":"info","message":"Navigation to https://hyperlinq.in successful","timestamp":"2025-06-28T16:20:30.878Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T16:20:30.879Z"}
{"level":"info","message":"Extracted metadata - Title: Hyperlinq Technology | Marketi..., Description: Transform your marketing strat...","timestamp":"2025-06-28T16:20:30.880Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T16:20:30.880Z"}
{"level":"info","message":"Extracted 60 links from the page","timestamp":"2025-06-28T16:20:30.881Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T16:20:30.881Z"}
{"level":"info","message":"Converted 7213 characters of HTML to text","timestamp":"2025-06-28T16:20:30.901Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T16:20:30.901Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T16:20:30.903Z"}
{"level":"info","message":"Extracting enhanced about data from: https://hyperlinq.in","timestamp":"2025-06-28T16:20:30.903Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://hyperlinq.in","timestamp":"2025-06-28T16:20:30.904Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:20:30.904Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:20:30.904Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-28T16:20:30.906Z","valuesCount":6}
{"level":"info","message":"Finding about and team pages from: https://hyperlinq.in","timestamp":"2025-06-28T16:20:30.906Z"}
{"level":"info","message":"Finding about and team pages for https://hyperlinq.in","timestamp":"2025-06-28T16:20:30.906Z"}
{"level":"info","message":"Found 2 potential about/team pages","timestamp":"2025-06-28T16:20:30.906Z"}
{"level":"info","message":"Found 2 about/team pages to extract","timestamp":"2025-06-28T16:20:30.907Z"}
{"level":"info","message":"Extracting enhanced about data from 2 pages","timestamp":"2025-06-28T16:20:30.907Z"}
{"level":"info","message":"Visiting page: https://hyperlinq.in/about","timestamp":"2025-06-28T16:20:30.907Z"}
{"level":"info","message":"API request received to scrape URL: https://www.ezrankings.com/","timestamp":"2025-06-28T16:20:31.068Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.ezrankings.com/","timestamp":"2025-06-28T16:20:31.068Z"}
{"level":"info","message":"Executing task for URL: https://www.ezrankings.com","timestamp":"2025-06-28T16:20:31.069Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://hyperlinq.in/about","timestamp":"2025-06-28T16:20:33.111Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:20:33.114Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:20:33.125Z"}
{"globalPresence":false,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-28T16:20:33.135Z","valuesCount":6}
{"level":"info","message":"Visiting page: https://www.linkedin.com/company/hyperlinq-technology/","timestamp":"2025-06-28T16:20:33.148Z"}
{"level":"info","message":"Navigation to https://www.ezrankings.com successful","timestamp":"2025-06-28T16:20:39.918Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T16:20:39.918Z"}
{"level":"info","message":"Extracted metadata - Title: Best Digital Marketing Agency ..., Description: Achieve up to 5X conversions w...","timestamp":"2025-06-28T16:20:39.919Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T16:20:39.919Z"}
{"level":"info","message":"Extracted 195 links from the page","timestamp":"2025-06-28T16:20:39.920Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T16:20:39.920Z"}
{"level":"info","message":"Converted 32256 characters of HTML to text","timestamp":"2025-06-28T16:20:39.945Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T16:20:39.945Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T16:20:39.946Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.ezrankings.com","timestamp":"2025-06-28T16:20:39.947Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com","timestamp":"2025-06-28T16:20:39.947Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:20:39.947Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:20:39.947Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":0,"timestamp":"2025-06-28T16:20:39.950Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.ezrankings.com","timestamp":"2025-06-28T16:20:39.950Z"}
{"level":"info","message":"Finding about and team pages for https://www.ezrankings.com","timestamp":"2025-06-28T16:20:39.950Z"}
{"level":"info","message":"Found 14 potential about/team pages","timestamp":"2025-06-28T16:20:39.951Z"}
{"level":"info","message":"Found 14 about/team pages to extract","timestamp":"2025-06-28T16:20:39.951Z"}
{"level":"info","message":"Extracting enhanced about data from 14 pages","timestamp":"2025-06-28T16:20:39.951Z"}
{"level":"info","message":"Visiting page: https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T16:20:39.952Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com/about-us.html","timestamp":"2025-06-28T16:20:41.189Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:20:41.189Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:20:41.190Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":3,"timestamp":"2025-06-28T16:20:41.194Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.ezrankings.com/seo-company-india.html","timestamp":"2025-06-28T16:20:41.194Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.linkedin.com/company/hyperlinq-technology/","timestamp":"2025-06-28T16:20:41.365Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:20:41.365Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:20:41.365Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T16:20:41.367Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://hyperlinq.in/ in 16988ms","timestamp":"2025-06-28T16:20:41.390Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.ezrankings.com/seo-company-india.html","timestamp":"2025-06-28T16:20:42.433Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:20:42.438Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:20:42.440Z"}
{"globalPresence":true,"hasDescription":true,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":4,"teamMembersCount":0,"timestamp":"2025-06-28T16:20:42.443Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.ezrankings.com/ in 11396ms","timestamp":"2025-06-28T16:20:42.464Z"}
{"level":"info","message":"API request received to scrape URL: hhttps://www.semrush.com/","timestamp":"2025-06-28T16:21:16.335Z"}
{"level":"info","message":"Starting scrape operation for URL: hhttps://www.semrush.com/","timestamp":"2025-06-28T16:21:16.335Z"}
{"level":"info","message":"Executing task for URL: hhttps://www.semrush.com/","timestamp":"2025-06-28T16:21:16.335Z"}
{"level":"warn","message":"Navigation attempt 1 failed for hhttps://www.semrush.com/: net::ERR_ABORTED at hhttps://www.semrush.com/","timestamp":"2025-06-28T16:21:16.494Z"}
{"level":"info","message":"Retrying... (1/2)","timestamp":"2025-06-28T16:21:16.494Z"}
{"level":"warn","message":"Navigation attempt 2 failed for hhttps://www.semrush.com/: net::ERR_ABORTED at hhttps://www.semrush.com/","timestamp":"2025-06-28T16:21:18.546Z"}
{"level":"info","message":"Retrying... (2/2)","timestamp":"2025-06-28T16:21:18.546Z"}
{"level":"warn","message":"Navigation attempt 3 failed for hhttps://www.semrush.com/: net::ERR_ABORTED at hhttps://www.semrush.com/","timestamp":"2025-06-28T16:21:20.584Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_ABORTED at hhttps://www.semrush.com/","stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_ABORTED at hhttps://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:25:6)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:168:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T16:21:20.595Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_ABORTED at hhttps://www.semrush.com/","stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_ABORTED at hhttps://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:25:6)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:168:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T16:21:20.596Z"}
{"level":"error","message":"Error in scrape controller: Error in BrowserController.executeWithPage: net::ERR_ABORTED at hhttps://www.semrush.com/","timestamp":"2025-06-28T16:21:20.596Z"}
{"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-28T16:21:20.876Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-28T16:21:20.877Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com","timestamp":"2025-06-28T16:21:20.877Z"}
{"level":"info","message":"Navigation to https://www.semrush.com successful","timestamp":"2025-06-28T16:21:24.466Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T16:21:24.467Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-28T16:21:24.469Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T16:21:24.469Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-28T16:21:24.471Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T16:21:24.471Z"}
{"level":"info","message":"Converted 15968 characters of HTML to text","timestamp":"2025-06-28T16:21:24.497Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T16:21:24.497Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T16:21:24.498Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.semrush.com","timestamp":"2025-06-28T16:21:24.499Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com","timestamp":"2025-06-28T16:21:24.499Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:21:24.499Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:21:24.499Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T16:21:24.501Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.semrush.com","timestamp":"2025-06-28T16:21:24.501Z"}
{"level":"info","message":"Finding about and team pages for https://www.semrush.com","timestamp":"2025-06-28T16:21:24.501Z"}
{"level":"info","message":"Found 11 potential about/team pages","timestamp":"2025-06-28T16:21:24.501Z"}
{"level":"info","message":"Found 11 about/team pages to extract","timestamp":"2025-06-28T16:21:24.501Z"}
{"level":"info","message":"Extracting enhanced about data from 11 pages","timestamp":"2025-06-28T16:21:24.501Z"}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/","timestamp":"2025-06-28T16:21:24.501Z"}
{"level":"error","message":"Error extracting from https://www.semrush.com/company/: Navigating frame was detached","timestamp":"2025-06-28T16:21:36.929Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:21:43.390Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T16:21:43.574Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:21:43.601Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:21:43.601Z"}
{"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-28T16:21:46.828Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-28T16:21:46.829Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T16:21:46.829Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-28T16:21:47.327Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com","timestamp":"2025-06-28T16:21:47.327Z"}
{"level":"info","message":"Navigation to https://www.semrush.com successful","timestamp":"2025-06-28T16:21:50.754Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T16:21:50.755Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-28T16:21:50.757Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T16:21:50.757Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-28T16:21:50.758Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T16:21:50.758Z"}
{"level":"info","message":"Converted 15968 characters of HTML to text","timestamp":"2025-06-28T16:21:50.790Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T16:21:50.790Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T16:21:50.791Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.semrush.com","timestamp":"2025-06-28T16:21:50.792Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com","timestamp":"2025-06-28T16:21:50.792Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:21:50.792Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:21:50.792Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T16:21:50.794Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.semrush.com","timestamp":"2025-06-28T16:21:50.794Z"}
{"level":"info","message":"Finding about and team pages for https://www.semrush.com","timestamp":"2025-06-28T16:21:50.794Z"}
{"level":"info","message":"Found 11 potential about/team pages","timestamp":"2025-06-28T16:21:50.794Z"}
{"level":"info","message":"Found 11 about/team pages to extract","timestamp":"2025-06-28T16:21:50.794Z"}
{"level":"info","message":"Extracting enhanced about data from 11 pages","timestamp":"2025-06-28T16:21:50.794Z"}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/","timestamp":"2025-06-28T16:21:50.794Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/","timestamp":"2025-06-28T16:22:16.023Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:22:16.024Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:22:16.024Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":true,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":25,"timestamp":"2025-06-28T16:22:16.030Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/stories/","timestamp":"2025-06-28T16:22:16.031Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: Timeout hit: 30000","stack":"ScraperError: Error in BrowserController.executeWithPage: Timeout hit: 30000\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:25:6)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:168:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T16:22:17.436Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.executeWithPage: Timeout hit: 30000","stack":"ScraperError: Error in BrowserController.executeWithPage: Timeout hit: 30000\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:25:6)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:168:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T16:22:17.436Z"}
{"level":"error","message":"Error in scrape controller: Error in BrowserController.executeWithPage: Timeout hit: 30000","timestamp":"2025-06-28T16:22:17.436Z"}
{"level":"error","message":"Error extracting from https://www.semrush.com/company/stories/: Navigating frame was detached","timestamp":"2025-06-28T16:22:17.527Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:24:25.959Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T16:24:26.204Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:24:26.236Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:24:26.236Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:24:51.978Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T16:24:52.151Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:24:52.172Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:24:52.172Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:26:42.386Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T16:26:42.579Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:26:42.607Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:26:42.607Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:28:05.322Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T16:28:05.514Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:28:05.540Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:28:05.540Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:36:42.131Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T16:36:42.359Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:36:42.387Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:36:42.387Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:37:11.785Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T16:37:11.952Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T16:37:11.976Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T16:37:11.976Z"}
{"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-28T16:40:38.106Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-28T16:40:38.107Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T16:40:38.108Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-28T16:40:39.077Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com (large website: true)","timestamp":"2025-06-28T16:40:39.077Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com (attempt 1/6)","timestamp":"2025-06-28T16:40:39.077Z"}
{"level":"info","message":"Navigation to https://www.semrush.com successful","timestamp":"2025-06-28T16:40:40.693Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T16:40:40.693Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-28T16:40:40.696Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T16:40:40.696Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-28T16:40:40.696Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T16:40:40.696Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-28T16:40:40.728Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T16:40:40.728Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T16:40:40.729Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.semrush.com","timestamp":"2025-06-28T16:40:40.729Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com","timestamp":"2025-06-28T16:40:40.729Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:40:40.730Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:40:40.730Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T16:40:40.731Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.semrush.com","timestamp":"2025-06-28T16:40:40.731Z"}
{"level":"info","message":"Finding about and team pages for https://www.semrush.com","timestamp":"2025-06-28T16:40:40.732Z"}
{"level":"info","message":"Found 11 potential about/team pages","timestamp":"2025-06-28T16:40:40.732Z"}
{"level":"info","message":"Found 11 about/team pages to extract","timestamp":"2025-06-28T16:40:40.732Z"}
{"level":"info","message":"Extracting enhanced about data from 11 pages","timestamp":"2025-06-28T16:40:40.732Z"}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/","timestamp":"2025-06-28T16:40:40.732Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/","timestamp":"2025-06-28T16:40:43.301Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:40:43.301Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:40:43.302Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":true,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":25,"timestamp":"2025-06-28T16:40:43.311Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/stories/","timestamp":"2025-06-28T16:40:43.311Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/stories/","timestamp":"2025-06-28T16:40:46.511Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T16:40:46.511Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T16:40:46.512Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T16:40:46.515Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.semrush.com/ in 8434ms","timestamp":"2025-06-28T16:40:46.541Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:37:09.934Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T18:37:10.167Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:37:10.197Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T18:37:10.197Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:37:10.198Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T18:37:10.198Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /","timestamp":"2025-06-28T18:42:59.296Z","userAgent":"PostmanRuntime/7.44.1"}
{"contentLength":1949,"duration":"2ms","level":"info","message":"AI Scraper API Response: POST /api/ai-scraper","statusCode":500,"timestamp":"2025-06-28T18:42:59.299Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T18:44:28.731Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T18:44:28.733Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T18:44:28.736Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T18:44:28.738Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-28T18:44:29.813Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/ (large website: true)","timestamp":"2025-06-28T18:44:29.813Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/ (attempt 1/6)","timestamp":"2025-06-28T18:44:29.813Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/ successful","timestamp":"2025-06-28T18:44:59.950Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T18:44:59.951Z"}
{"code":"UNKNOWN_ERROR","context":"ContentExtractor.getTitleAndDesc","level":"error","message":"Error in ContentExtractor.getTitleAndDesc: Protocol error (Runtime.callFunctionOn): Target closed","retryable":false,"stack":"ScraperError: Error in ContentExtractor.getTitleAndDesc: Protocol error (Runtime.callFunctionOn): Target closed\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at ContentExtractor.getTitleAndDesc (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/content-extractor.ts:67:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:178:26)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:208:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/puppeteer-cluster/util.ts:85:13)","statusCode":500,"timestamp":"2025-06-28T18:44:59.956Z"}
{"config":{"burstLimit":1,"delayMs":5000,"maxRequests":3,"windowMs":60000},"domain":"semrush.com","errorCount":1,"level":"info","message":"Adjusted rate limit for semrush.com","timestamp":"2025-06-28T18:44:59.956Z"}
{"domain":"semrush.com","error":"Error in ContentExtractor.getTitleAndDesc: Protocol error (Runtime.callFunctionOn): Target closed","level":"warn","message":"Recorded error for semrush.com, total errors: 1","timestamp":"2025-06-28T18:44:59.957Z"}
{"delay":7237.873921249035,"error":"Timeout hit: 30000","level":"warn","message":"large-website-scrape-https://www.semrush.com/ failed on attempt 1, retrying in 7237.873921249035ms:","nextAttempt":2,"timestamp":"2025-06-28T18:44:59.966Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/ (attempt 2/6)","timestamp":"2025-06-28T18:45:07.205Z"}
{"config":{"burstLimit":1,"delayMs":5000,"maxRequests":3,"windowMs":60000},"domain":"semrush.com","errorCount":2,"level":"info","message":"Adjusted rate limit for semrush.com","timestamp":"2025-06-28T18:45:22.919Z"}
{"domain":"semrush.com","error":"net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/","level":"warn","message":"Recorded error for semrush.com, total errors: 2","timestamp":"2025-06-28T18:45:22.920Z"}
{"attempt":2,"error":"net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/","level":"error","message":"large-website-scrape-https://www.semrush.com/ failed with non-retryable error:","timestamp":"2025-06-28T18:45:22.932Z"}
{"code":"NAVIGATION_FAILED","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/","retryAfter":15,"retryable":true,"stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":502,"timestamp":"2025-06-28T18:45:22.934Z"}
{"code":"NAVIGATION_FAILED","context":"AIEnhancedScraperService.scrapeWithAI","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/","retryAfter":15,"retryable":true,"stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":502,"timestamp":"2025-06-28T18:45:22.935Z"}
{"error":"Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/","level":"error","message":"AI-enhanced scrape failed","stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","timestamp":"2025-06-28T18:45:22.935Z","url":"https://www.semrush.com/"}
{"contentLength":"461","duration":"54206ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":500,"timestamp":"2025-06-28T18:45:22.938Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:46:28.490Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T18:46:28.672Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:46:28.698Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T18:46:28.698Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:46:28.699Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T18:46:28.699Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T18:46:33.002Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T18:46:33.003Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T18:46:33.004Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T18:46:33.005Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-28T18:46:33.517Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/ (large website: true)","timestamp":"2025-06-28T18:46:33.518Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/ (attempt 1/6)","timestamp":"2025-06-28T18:46:33.518Z"}
{"config":{"burstLimit":1,"delayMs":5000,"maxRequests":3,"windowMs":60000},"domain":"semrush.com","errorCount":1,"level":"info","message":"Adjusted rate limit for semrush.com","timestamp":"2025-06-28T18:46:33.656Z"}
{"domain":"semrush.com","error":"net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/","level":"warn","message":"Recorded error for semrush.com, total errors: 1","timestamp":"2025-06-28T18:46:33.657Z"}
{"attempt":1,"error":"net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/","level":"error","message":"large-website-scrape-https://www.semrush.com/ failed with non-retryable error:","timestamp":"2025-06-28T18:46:33.668Z"}
{"code":"NAVIGATION_FAILED","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/","retryAfter":15,"retryable":true,"stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":502,"timestamp":"2025-06-28T18:46:33.670Z"}
{"code":"NAVIGATION_FAILED","context":"AIEnhancedScraperService.scrapeWithAI","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/","retryAfter":15,"retryable":true,"stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":502,"timestamp":"2025-06-28T18:46:33.670Z"}
{"error":"Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/","level":"error","message":"AI-enhanced scrape failed","stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","timestamp":"2025-06-28T18:46:33.670Z","url":"https://www.semrush.com/"}
{"contentLength":"462","duration":"668ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":500,"timestamp":"2025-06-28T18:46:33.671Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:46:47.140Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T18:46:47.302Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:46:47.325Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T18:46:47.325Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:46:47.326Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T18:46:47.326Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T18:46:49.591Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T18:46:49.591Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T18:46:49.593Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T18:46:49.594Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-28T18:46:49.995Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/ (large website: true)","timestamp":"2025-06-28T18:46:49.995Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/ (attempt 1/6)","timestamp":"2025-06-28T18:46:49.995Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/ successful","timestamp":"2025-06-28T18:46:51.681Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T18:46:51.681Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-28T18:46:51.685Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T18:46:51.685Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-28T18:46:51.688Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T18:46:51.688Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-28T18:46:51.712Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T18:46:51.712Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T18:46:51.714Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/","timestamp":"2025-06-28T18:46:51.714Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T18:46:51.715Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T18:46:51.715Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T18:46:51.717Z","valuesCount":0}
{"level":"info","message":"Estimated AI processing cost: $0.1521","timestamp":"2025-06-28T18:46:51.732Z"}
{"aiCost":0,"aiTime":2850,"confidence":0,"level":"info","message":"AI-enhanced scrape completed for https://www.semrush.com/","timestamp":"2025-06-28T18:46:54.583Z","totalTime":4990}
{"aiCost":0,"confidence":0,"level":"info","message":"AI-enhanced scrape completed successfully","status":"success","timestamp":"2025-06-28T18:46:54.583Z","url":"https://www.semrush.com/"}
{"contentLength":"24105","duration":"4996ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":200,"timestamp":"2025-06-28T18:46:54.587Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T18:50:46.638Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T18:50:46.639Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T18:50:46.641Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/ (large website: true)","timestamp":"2025-06-28T18:50:46.642Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/ (attempt 1/6)","timestamp":"2025-06-28T18:50:46.642Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/ successful","timestamp":"2025-06-28T18:50:49.045Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T18:50:49.045Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-28T18:50:49.048Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T18:50:49.048Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-28T18:50:49.049Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T18:50:49.050Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-28T18:50:49.074Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T18:50:49.075Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T18:50:49.075Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/","timestamp":"2025-06-28T18:50:49.076Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T18:50:49.076Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T18:50:49.076Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T18:50:49.078Z","valuesCount":0}
{"level":"info","message":"Estimated AI processing cost: $0.1521","timestamp":"2025-06-28T18:50:49.085Z"}
{"level":"warn","message":"AI processing skipped due to estimated cost: Request cost exceeds per-request limit. Cost: $0.1521, Limit: $0.1","timestamp":"2025-06-28T18:50:49.085Z"}
{"aiCost":0,"confidence":0,"level":"info","message":"AI-enhanced scrape completed successfully","status":"partial","timestamp":"2025-06-28T18:50:49.085Z","url":"https://www.semrush.com/"}
{"contentLength":"24146","duration":"2447ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":200,"timestamp":"2025-06-28T18:50:49.086Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:51:12.432Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T18:51:12.632Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:51:12.659Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T18:51:12.659Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:51:12.661Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T18:51:12.661Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:55:51.930Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T18:55:52.109Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:55:52.138Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T18:55:52.138Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T18:55:52.140Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T18:55:52.140Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T18:55:57.765Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T18:55:57.766Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T18:55:57.767Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T18:55:57.767Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-28T18:55:58.656Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/ (large website: true)","timestamp":"2025-06-28T18:55:58.656Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/ (attempt 1/6)","timestamp":"2025-06-28T18:55:58.657Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/ successful","timestamp":"2025-06-28T18:56:00.495Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T18:56:00.495Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-28T18:56:00.497Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T18:56:00.498Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-28T18:56:00.498Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T18:56:00.499Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-28T18:56:00.521Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T18:56:00.521Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T18:56:00.522Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/","timestamp":"2025-06-28T18:56:00.522Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T18:56:00.522Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T18:56:00.522Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T18:56:00.524Z","valuesCount":0}
{"level":"info","message":"Estimated AI processing cost: $0.1521","timestamp":"2025-06-28T18:56:00.534Z"}
{"level":"warn","message":"AI processing skipped due to estimated cost: Request cost exceeds per-request limit. Cost: $0.1521, Limit: $0.1","timestamp":"2025-06-28T18:56:00.534Z"}
{"aiCost":0,"confidence":0,"level":"info","message":"AI-enhanced scrape completed successfully","status":"partial","timestamp":"2025-06-28T18:56:00.535Z","url":"https://www.semrush.com/"}
{"contentLength":"24146","duration":"2771ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":200,"timestamp":"2025-06-28T18:56:00.536Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T18:56:13.141Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T18:56:13.141Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T18:56:13.142Z"}
{"level":"info","message":"Rate limiter waiting 14625ms before next request","timestamp":"2025-06-28T18:56:13.142Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/ (large website: true)","timestamp":"2025-06-28T18:56:27.769Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/ (attempt 1/6)","timestamp":"2025-06-28T18:56:27.770Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/ successful","timestamp":"2025-06-28T18:56:29.371Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T18:56:29.371Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-28T18:56:29.373Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T18:56:29.373Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-28T18:56:29.374Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T18:56:29.374Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-28T18:56:29.397Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T18:56:29.397Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T18:56:29.398Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/","timestamp":"2025-06-28T18:56:29.398Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T18:56:29.399Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T18:56:29.399Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T18:56:29.400Z","valuesCount":0}
{"level":"info","message":"Estimated AI processing cost: $0.1521","timestamp":"2025-06-28T18:56:29.410Z"}
{"aiCost":0,"aiTime":3244,"confidence":80,"level":"info","message":"AI-enhanced scrape completed for https://www.semrush.com/","timestamp":"2025-06-28T18:56:32.654Z","totalTime":19512}
{"aiCost":0,"confidence":80,"level":"info","message":"AI-enhanced scrape completed successfully","status":"success","timestamp":"2025-06-28T18:56:32.655Z","url":"https://www.semrush.com/"}
{"contentLength":"24846","duration":"19516ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":200,"timestamp":"2025-06-28T18:56:32.657Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T18:57:00.943Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T18:57:00.944Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"error":"[\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 1,\n    \"type\": \"number\",\n    \"inclusive\": true,\n    \"exact\": false,\n    \"message\": \"Number must be less than or equal to 1\",\n    \"path\": [\n      \"options\",\n      \"aiOptions\",\n      \"maxCost\"\n    ]\n  }\n]","level":"error","message":"AI-enhanced scrape failed","stack":"ZodError: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 1,\n    \"type\": \"number\",\n    \"inclusive\": true,\n    \"exact\": false,\n    \"message\": \"Number must be less than or equal to 1\",\n    \"path\": [\n      \"options\",\n      \"aiOptions\",\n      \"maxCost\"\n    ]\n  }\n]\n    at get error (file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/zod@3.24.2/node_modules/zod/lib/index.mjs:587:31)\n    at ZodObject.parse (file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/zod@3.24.2/node_modules/zod/lib/index.mjs:663:22)\n    at AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:67:49)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:280:10)","timestamp":"2025-06-28T18:57:00.952Z","url":"https://www.semrush.com/"}
{"contentLength":"275","duration":"10ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":400,"timestamp":"2025-06-28T18:57:00.953Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T18:57:07.764Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T18:57:07.764Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T18:57:07.764Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/ (large website: true)","timestamp":"2025-06-28T18:57:07.765Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/ (attempt 1/6)","timestamp":"2025-06-28T18:57:07.765Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/ successful","timestamp":"2025-06-28T18:57:09.409Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T18:57:09.410Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-28T18:57:09.411Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T18:57:09.412Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-28T18:57:09.412Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T18:57:09.412Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-28T18:57:09.426Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T18:57:09.428Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T18:57:09.430Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/","timestamp":"2025-06-28T18:57:09.430Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T18:57:09.430Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T18:57:09.431Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T18:57:09.432Z","valuesCount":0}
{"level":"info","message":"Estimated AI processing cost: $0.1521","timestamp":"2025-06-28T18:57:09.443Z"}
{"aiCost":0,"aiTime":1,"confidence":80,"level":"info","message":"AI-enhanced scrape completed for https://www.semrush.com/","timestamp":"2025-06-28T18:57:09.445Z","totalTime":1681}
{"aiCost":0,"confidence":80,"level":"info","message":"AI-enhanced scrape completed successfully","status":"success","timestamp":"2025-06-28T18:57:09.445Z","url":"https://www.semrush.com/"}
{"contentLength":"24838","duration":"1682ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":200,"timestamp":"2025-06-28T18:57:09.447Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:19:01.754Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T19:19:01.936Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:19:01.965Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T19:19:01.965Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:19:01.966Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T19:19:01.967Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T19:19:06.319Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T19:19:06.320Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T19:19:06.321Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T19:19:06.323Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-28T19:19:07.200Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/ (large website: true)","timestamp":"2025-06-28T19:19:07.200Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/ (attempt 1/6)","timestamp":"2025-06-28T19:19:07.200Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/ successful","timestamp":"2025-06-28T19:19:09.028Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T19:19:09.028Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-28T19:19:09.031Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T19:19:09.031Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-28T19:19:09.032Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T19:19:09.032Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-28T19:19:09.060Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T19:19:09.060Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T19:19:09.061Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/","timestamp":"2025-06-28T19:19:09.062Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T19:19:09.062Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T19:19:09.062Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T19:19:09.064Z","valuesCount":0}
{"level":"info","message":"Estimated AI processing cost: $0.1521","timestamp":"2025-06-28T19:19:09.075Z"}
{"aiCost":0,"aiTime":4579,"confidence":85,"level":"info","message":"AI-enhanced scrape completed for https://www.semrush.com/","timestamp":"2025-06-28T19:19:13.655Z","totalTime":7334}
{"aiCost":0,"confidence":85,"level":"info","message":"AI-enhanced scrape completed successfully","status":"success","timestamp":"2025-06-28T19:19:13.656Z","url":"https://www.semrush.com/"}
{"contentLength":"24844","duration":"7340ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":200,"timestamp":"2025-06-28T19:19:13.660Z"}
{"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-28T19:20:07.934Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-28T19:20:07.935Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T19:20:07.937Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.initCluster","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:29:19)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_zshffmfwcf2zx26zplrcc5hpue/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","statusCode":500,"timestamp":"2025-06-28T19:20:08.090Z"}
{"code":"UNKNOWN_ERROR","error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","isLargeWebsite":true,"level":"error","message":"Failed to execute task for https://www.semrush.com:","retryable":false,"statusCode":500,"timestamp":"2025-06-28T19:20:08.091Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:29:19)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_zshffmfwcf2zx26zplrcc5hpue/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","statusCode":500,"timestamp":"2025-06-28T19:20:08.091Z"}
{"code":"UNKNOWN_ERROR","level":"error","message":"Error in scrape controller: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"statusCode":500,"timestamp":"2025-06-28T19:20:08.091Z","url":"https://www.semrush.com/"}
{"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-28T19:20:16.214Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-28T19:20:16.215Z"}
{"level":"info","message":"Rate limiter waiting 21722ms before next request","timestamp":"2025-06-28T19:20:16.218Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T19:20:37.942Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.initCluster","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:29:19)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_zshffmfwcf2zx26zplrcc5hpue/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","statusCode":500,"timestamp":"2025-06-28T19:20:38.103Z"}
{"code":"UNKNOWN_ERROR","error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","isLargeWebsite":true,"level":"error","message":"Failed to execute task for https://www.semrush.com:","retryable":false,"statusCode":500,"timestamp":"2025-06-28T19:20:38.104Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:29:19)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_zshffmfwcf2zx26zplrcc5hpue/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","statusCode":500,"timestamp":"2025-06-28T19:20:38.104Z"}
{"code":"UNKNOWN_ERROR","level":"error","message":"Error in scrape controller: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"statusCode":500,"timestamp":"2025-06-28T19:20:38.104Z","url":"https://www.semrush.com/"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:23:55.020Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138635202_mmib6zbe4\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T19:23:55.202Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:23:55.229Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T19:23:55.229Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:23:55.230Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T19:23:55.230Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:24:25.503Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138665687_jf7ivjnl5\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T19:24:25.687Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:24:25.709Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T19:24:25.709Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:24:25.710Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T19:24:25.710Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:24:51.799Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138691978_okefpdzs6\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T19:24:51.978Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:24:52.005Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T19:24:52.006Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:24:52.007Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T19:24:52.008Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:25:06.190Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138706368_pceiu7rzs\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T19:25:06.369Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:25:06.391Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T19:25:06.391Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:25:06.393Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T19:25:06.393Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:25:20.771Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138720972_gd4brlgtc\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T19:25:20.972Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:25:21.009Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T19:25:21.009Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:25:21.011Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T19:25:21.011Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:25:33.896Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138734068_tvu5jhed7\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T19:25:34.068Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:25:34.091Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T19:25:34.091Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:25:34.093Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T19:25:34.093Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:26:49.215Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T19:26:49.399Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:26:49.424Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T19:26:49.424Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:26:49.426Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T19:26:49.426Z"}
{"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-28T19:28:04.797Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-28T19:28:04.798Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T19:28:04.799Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-28T19:28:05.808Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com (large website: true)","timestamp":"2025-06-28T19:28:05.809Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com (attempt 1/6)","timestamp":"2025-06-28T19:28:05.809Z"}
{"level":"info","message":"Navigation to https://www.semrush.com successful","timestamp":"2025-06-28T19:28:07.748Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T19:28:07.748Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-28T19:28:07.753Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T19:28:07.753Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-28T19:28:07.754Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T19:28:07.754Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-28T19:28:07.780Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T19:28:07.780Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T19:28:07.781Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.semrush.com","timestamp":"2025-06-28T19:28:07.781Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com","timestamp":"2025-06-28T19:28:07.781Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T19:28:07.782Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T19:28:07.782Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T19:28:07.783Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.semrush.com","timestamp":"2025-06-28T19:28:07.784Z"}
{"level":"info","message":"Finding about and team pages for https://www.semrush.com","timestamp":"2025-06-28T19:28:07.784Z"}
{"level":"info","message":"Found 11 potential about/team pages","timestamp":"2025-06-28T19:28:07.784Z"}
{"level":"info","message":"Found 11 about/team pages to extract","timestamp":"2025-06-28T19:28:07.784Z"}
{"level":"info","message":"Extracting enhanced about data from 11 pages","timestamp":"2025-06-28T19:28:07.784Z"}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/","timestamp":"2025-06-28T19:28:07.784Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/","timestamp":"2025-06-28T19:28:09.460Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T19:28:09.461Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T19:28:09.461Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":true,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":25,"timestamp":"2025-06-28T19:28:09.470Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/stories/","timestamp":"2025-06-28T19:28:09.471Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/stories/","timestamp":"2025-06-28T19:28:14.168Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T19:28:14.169Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T19:28:14.169Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T19:28:14.173Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.semrush.com/ in 9403ms","timestamp":"2025-06-28T19:28:14.200Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T19:28:41.959Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T19:28:41.960Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T19:28:41.963Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T19:28:41.965Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.initCluster","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":500,"timestamp":"2025-06-28T19:28:42.070Z"}
{"code":"UNKNOWN_ERROR","error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","isLargeWebsite":true,"level":"error","message":"Failed to execute task for https://www.semrush.com/:","retryable":false,"statusCode":500,"timestamp":"2025-06-28T19:28:42.071Z"}
{"code":"UNKNOWN_ERROR","context":"AIEnhancedScraperService.scrapeWithAI","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":500,"timestamp":"2025-06-28T19:28:42.071Z"}
{"error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","level":"error","message":"AI-enhanced scrape failed","stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","timestamp":"2025-06-28T19:28:42.071Z","url":"https://www.semrush.com/"}
{"contentLength":"1116","duration":"112ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":500,"timestamp":"2025-06-28T19:28:42.071Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T19:28:50.490Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T19:28:50.491Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T19:28:50.492Z"}
{"level":"info","message":"Rate limiter waiting 21472ms before next request","timestamp":"2025-06-28T19:28:50.493Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T19:29:11.966Z"}
{"code":"BLOCKED_BY_WEBSITE","context":"BrowserController.initCluster","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":403,"timestamp":"2025-06-28T19:29:12.110Z"}
{"code":"BLOCKED_BY_WEBSITE","error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","isLargeWebsite":true,"level":"error","message":"Failed to execute task for https://www.semrush.com/:","retryable":false,"statusCode":403,"timestamp":"2025-06-28T19:29:12.110Z"}
{"code":"BLOCKED_BY_WEBSITE","context":"AIEnhancedScraperService.scrapeWithAI","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":403,"timestamp":"2025-06-28T19:29:12.110Z"}
{"error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","level":"error","message":"AI-enhanced scrape failed","stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","timestamp":"2025-06-28T19:29:12.110Z","url":"https://www.semrush.com/"}
{"contentLength":"1116","duration":"21620ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":500,"timestamp":"2025-06-28T19:29:12.111Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T19:29:20.224Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T19:29:20.226Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T19:29:20.227Z"}
{"level":"info","message":"Rate limiter waiting 21737ms before next request","timestamp":"2025-06-28T19:29:20.228Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:30:21.837Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751139022045_ymcv23hem\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-28T19:30:22.045Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:30:22.088Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-28T19:30:22.088Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-28T19:30:22.090Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-28T19:30:22.090Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-28T19:30:37.167Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-28T19:30:37.168Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-28T19:30:37.170Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-28T19:30:37.171Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-28T19:30:38.156Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/ (large website: true)","timestamp":"2025-06-28T19:30:38.156Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/ (attempt 1/6)","timestamp":"2025-06-28T19:30:38.156Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/ successful","timestamp":"2025-06-28T19:30:40.203Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-28T19:30:40.203Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-28T19:30:40.206Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-28T19:30:40.206Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-28T19:30:40.207Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-28T19:30:40.207Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-28T19:30:40.236Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-28T19:30:40.236Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-28T19:30:40.237Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/","timestamp":"2025-06-28T19:30:40.238Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-28T19:30:40.238Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-28T19:30:40.238Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-28T19:30:40.240Z","valuesCount":0}
{"level":"info","message":"Found 5 important pages to analyze","timestamp":"2025-06-28T19:30:40.253Z"}
{"level":"info","message":"Scraping important page: company - https://www.semrush.com/company/","timestamp":"2025-06-28T19:30:40.253Z"}
{"level":"info","message":"Rate limiter waiting 26918ms before next request","timestamp":"2025-06-28T19:30:40.253Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/company/ (large website: true)","timestamp":"2025-06-28T19:31:07.176Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/company/ (attempt 1/6)","timestamp":"2025-06-28T19:31:07.178Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/company/ successful","timestamp":"2025-06-28T19:31:09.780Z"}
{"level":"info","message":"Successfully scraped company page: 5732 characters","timestamp":"2025-06-28T19:31:09.796Z"}
{"level":"info","message":"Scraping important page: company - https://www.semrush.com/company/stories/","timestamp":"2025-06-28T19:31:10.296Z"}
{"level":"info","message":"Rate limiter waiting 26877ms before next request","timestamp":"2025-06-28T19:31:10.296Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/company/stories/ (large website: true)","timestamp":"2025-06-28T19:31:37.175Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/company/stories/ (attempt 1/6)","timestamp":"2025-06-28T19:31:37.175Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/company/stories/ successful","timestamp":"2025-06-28T19:31:40.663Z"}
{"level":"info","message":"Successfully scraped company page: 1266 characters","timestamp":"2025-06-28T19:31:40.687Z"}
{"level":"info","message":"Scraping important page: company - https://www.semrush.com/company/partner-integrations/","timestamp":"2025-06-28T19:31:41.188Z"}
{"level":"info","message":"Rate limiter waiting 25985ms before next request","timestamp":"2025-06-28T19:31:41.189Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/company/partner-integrations/ (large website: true)","timestamp":"2025-06-28T19:32:07.182Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/company/partner-integrations/ (attempt 1/6)","timestamp":"2025-06-28T19:32:07.184Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/company/partner-integrations/ successful","timestamp":"2025-06-28T19:32:10.403Z"}
{"level":"info","message":"Successfully scraped company page: 2998 characters","timestamp":"2025-06-28T19:32:10.417Z"}
{"level":"info","message":"Scraping important page: contact - https://www.semrush.com/company/contacts/","timestamp":"2025-06-28T19:32:10.919Z"}
{"level":"info","message":"Rate limiter waiting 26262ms before next request","timestamp":"2025-06-28T19:32:10.919Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/company/contacts/ (large website: true)","timestamp":"2025-06-28T19:32:37.184Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/company/contacts/ (attempt 1/6)","timestamp":"2025-06-28T19:32:37.185Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/company/contacts/ successful","timestamp":"2025-06-28T19:32:39.674Z"}
{"level":"info","message":"Successfully scraped contact page: 641 characters","timestamp":"2025-06-28T19:32:39.686Z"}
{"level":"info","message":"Scraping important page: company - https://www.semrush.com/company/","timestamp":"2025-06-28T19:32:40.187Z"}
{"level":"info","message":"Rate limiter waiting 26994ms before next request","timestamp":"2025-06-28T19:32:40.188Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/company/ (large website: true)","timestamp":"2025-06-28T19:33:07.185Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/company/ (attempt 1/6)","timestamp":"2025-06-28T19:33:07.186Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/company/ successful","timestamp":"2025-06-28T19:33:10.920Z"}
{"level":"info","message":"Successfully scraped company page: 5732 characters","timestamp":"2025-06-28T19:33:10.944Z"}
{"level":"info","message":"Combined content length: 18676 characters from 3 pages","timestamp":"2025-06-28T19:33:11.446Z"}
{"level":"info","message":"Estimated AI processing cost: $0.3220","timestamp":"2025-06-28T19:33:11.447Z"}
{"aiCost":0,"aiTime":4151,"confidence":80,"level":"info","message":"AI-enhanced scrape completed for https://www.semrush.com/","timestamp":"2025-06-28T19:33:15.599Z","totalTime":158429}
{"aiCost":0,"confidence":80,"level":"info","message":"AI-enhanced scrape completed successfully","status":"success","timestamp":"2025-06-28T19:33:15.600Z","url":"https://www.semrush.com/"}
{"contentLength":"24811","duration":"158435ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":200,"timestamp":"2025-06-28T19:33:15.603Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T06:41:53.050Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751179313248_xgjtxogwn\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T06:41:53.248Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T06:41:53.279Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-29T06:41:53.279Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T06:41:53.281Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T06:41:53.281Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-29T06:42:02.814Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-29T06:42:02.815Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-29T06:42:02.816Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-29T06:42:02.817Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-29T06:42:04.246Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/ (large website: true)","timestamp":"2025-06-29T06:42:04.246Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/ (attempt 1/6)","timestamp":"2025-06-29T06:42:04.246Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/ successful","timestamp":"2025-06-29T06:42:05.991Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T06:42:05.992Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-29T06:42:05.995Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T06:42:05.995Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-29T06:42:05.996Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T06:42:05.996Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-29T06:42:06.024Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T06:42:06.025Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T06:42:06.026Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/","timestamp":"2025-06-29T06:42:06.027Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T06:42:06.027Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T06:42:06.027Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T06:42:06.029Z","valuesCount":0}
{"level":"info","message":"Found 5 important pages to analyze","timestamp":"2025-06-29T06:42:06.042Z"}
{"level":"info","message":"Scraping important page: company - https://www.semrush.com/company/","timestamp":"2025-06-29T06:42:06.043Z"}
{"level":"info","message":"Rate limiter waiting 26774ms before next request","timestamp":"2025-06-29T06:42:06.043Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/company/ (large website: true)","timestamp":"2025-06-29T06:42:32.819Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/company/ (attempt 1/6)","timestamp":"2025-06-29T06:42:32.820Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/company/ successful","timestamp":"2025-06-29T06:42:34.310Z"}
{"level":"info","message":"Successfully scraped company page: 5732 characters","timestamp":"2025-06-29T06:42:34.332Z"}
{"level":"info","message":"Scraping important page: company - https://www.semrush.com/company/stories/","timestamp":"2025-06-29T06:42:34.833Z"}
{"level":"info","message":"Rate limiter waiting 27985ms before next request","timestamp":"2025-06-29T06:42:34.833Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/company/stories/ (large website: true)","timestamp":"2025-06-29T06:43:02.823Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/company/stories/ (attempt 1/6)","timestamp":"2025-06-29T06:43:02.824Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/company/stories/ successful","timestamp":"2025-06-29T06:43:04.227Z"}
{"level":"info","message":"Successfully scraped company page: 1266 characters","timestamp":"2025-06-29T06:43:04.243Z"}
{"level":"info","message":"Scraping important page: company - https://www.semrush.com/company/partner-integrations/","timestamp":"2025-06-29T06:43:04.745Z"}
{"level":"info","message":"Rate limiter waiting 28074ms before next request","timestamp":"2025-06-29T06:43:04.746Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/company/partner-integrations/ (large website: true)","timestamp":"2025-06-29T06:43:32.821Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/company/partner-integrations/ (attempt 1/6)","timestamp":"2025-06-29T06:43:32.821Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/company/partner-integrations/ successful","timestamp":"2025-06-29T06:43:34.138Z"}
{"level":"info","message":"Successfully scraped company page: 2998 characters","timestamp":"2025-06-29T06:43:34.151Z"}
{"level":"info","message":"Scraping important page: contact - https://www.semrush.com/company/contacts/","timestamp":"2025-06-29T06:43:34.653Z"}
{"level":"info","message":"Rate limiter waiting 28166ms before next request","timestamp":"2025-06-29T06:43:34.654Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/company/contacts/ (large website: true)","timestamp":"2025-06-29T06:44:02.822Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/company/contacts/ (attempt 1/6)","timestamp":"2025-06-29T06:44:02.823Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/company/contacts/ successful","timestamp":"2025-06-29T06:44:04.237Z"}
{"level":"info","message":"Successfully scraped contact page: 641 characters","timestamp":"2025-06-29T06:44:04.252Z"}
{"level":"info","message":"Scraping important page: company - https://www.semrush.com/company/","timestamp":"2025-06-29T06:44:04.753Z"}
{"level":"info","message":"Rate limiter waiting 28067ms before next request","timestamp":"2025-06-29T06:44:04.753Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com/company/ (large website: true)","timestamp":"2025-06-29T06:44:32.823Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com/company/ (attempt 1/6)","timestamp":"2025-06-29T06:44:32.824Z"}
{"level":"info","message":"Navigation to https://www.semrush.com/company/ successful","timestamp":"2025-06-29T06:44:34.291Z"}
{"level":"info","message":"Successfully scraped company page: 5732 characters","timestamp":"2025-06-29T06:44:34.307Z"}
{"level":"info","message":"Combined content length: 18676 characters from 3 pages","timestamp":"2025-06-29T06:44:34.809Z"}
{"level":"info","message":"Estimated AI processing cost: $0.3220","timestamp":"2025-06-29T06:44:34.811Z"}
{"aiCost":0,"aiTime":2861,"confidence":80,"level":"info","message":"AI-enhanced scrape completed for https://www.semrush.com/","timestamp":"2025-06-29T06:44:37.673Z","totalTime":154857}
{"aiCost":0,"confidence":80,"level":"info","message":"AI-enhanced scrape completed successfully","status":"success","timestamp":"2025-06-29T06:44:37.673Z","url":"https://www.semrush.com/"}
{"contentLength":"24829","duration":"154863ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":200,"timestamp":"2025-06-29T06:44:37.677Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T07:20:42.195Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751181642382_dc2netxca\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T07:20:42.382Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T07:20:42.408Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-29T07:20:42.408Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T07:20:42.409Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T07:20:42.409Z"}
{"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-29T07:20:50.958Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-29T07:20:50.959Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-29T07:20:50.960Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-29T07:20:51.597Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com (large website: true)","timestamp":"2025-06-29T07:20:51.597Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com (attempt 1/6)","timestamp":"2025-06-29T07:20:51.597Z"}
{"level":"info","message":"Navigation to https://www.semrush.com successful","timestamp":"2025-06-29T07:20:53.069Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T07:20:53.069Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-29T07:20:53.073Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T07:20:53.073Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-29T07:20:53.076Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T07:20:53.076Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-29T07:20:53.099Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T07:20:53.100Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T07:20:53.101Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.semrush.com","timestamp":"2025-06-29T07:20:53.101Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com","timestamp":"2025-06-29T07:20:53.101Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:20:53.101Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:20:53.101Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T07:20:53.103Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.semrush.com","timestamp":"2025-06-29T07:20:53.103Z"}
{"level":"info","message":"Finding about and team pages for https://www.semrush.com","timestamp":"2025-06-29T07:20:53.103Z"}
{"level":"info","message":"Found 11 potential about/team pages","timestamp":"2025-06-29T07:20:53.104Z"}
{"level":"info","message":"Found 11 about/team pages to extract","timestamp":"2025-06-29T07:20:53.104Z"}
{"level":"info","message":"Extracting enhanced about data from 11 pages","timestamp":"2025-06-29T07:20:53.104Z"}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/","timestamp":"2025-06-29T07:20:53.104Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/","timestamp":"2025-06-29T07:20:55.138Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:20:55.138Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:20:55.139Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":true,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":25,"timestamp":"2025-06-29T07:20:55.147Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/stories/","timestamp":"2025-06-29T07:20:55.148Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/stories/","timestamp":"2025-06-29T07:20:58.619Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:20:58.619Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:20:58.620Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T07:20:58.623Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.semrush.com/ in 7700ms","timestamp":"2025-06-29T07:20:58.659Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-29T07:21:04.731Z","userAgent":"PostmanRuntime/7.44.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-29T07:21:04.732Z","url":"https://www.semrush.com/","userAgent":"PostmanRuntime/7.44.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://www.semrush.com/","timestamp":"2025-06-29T07:21:04.734Z"}
{"level":"info","message":"Rate limiter waiting 16225ms before next request","timestamp":"2025-06-29T07:21:04.734Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-29T07:21:20.961Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.initCluster","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[62446:11384612:0629/125121.086741:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751181642382_dc2netxca/SingletonLock: File exists (17)\n[62446:11384612:0629/125121.086940:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[62446:11384612:0629/125121.086741:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751181642382_dc2netxca/SingletonLock: File exists (17)\n[62446:11384612:0629/125121.086940:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":500,"timestamp":"2025-06-29T07:21:21.094Z"}
{"code":"UNKNOWN_ERROR","error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[62446:11384612:0629/125121.086741:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751181642382_dc2netxca/SingletonLock: File exists (17)\n[62446:11384612:0629/125121.086940:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","isLargeWebsite":true,"level":"error","message":"Failed to execute task for https://www.semrush.com/:","retryable":false,"statusCode":500,"timestamp":"2025-06-29T07:21:21.095Z"}
{"code":"UNKNOWN_ERROR","context":"AIEnhancedScraperService.scrapeWithAI","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[62446:11384612:0629/125121.086741:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751181642382_dc2netxca/SingletonLock: File exists (17)\n[62446:11384612:0629/125121.086940:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[62446:11384612:0629/125121.086741:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751181642382_dc2netxca/SingletonLock: File exists (17)\n[62446:11384612:0629/125121.086940:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":500,"timestamp":"2025-06-29T07:21:21.095Z"}
{"error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[62446:11384612:0629/125121.086741:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751181642382_dc2netxca/SingletonLock: File exists (17)\n[62446:11384612:0629/125121.086940:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","level":"error","message":"AI-enhanced scrape failed","stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[62446:11384612:0629/125121.086741:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751181642382_dc2netxca/SingletonLock: File exists (17)\n[62446:11384612:0629/125121.086940:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","timestamp":"2025-06-29T07:21:21.095Z","url":"https://www.semrush.com/"}
{"contentLength":"1116","duration":"16364ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":500,"timestamp":"2025-06-29T07:21:21.096Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T07:36:33.197Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751182593369_hfbtmi6m9\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T07:36:33.369Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T07:36:33.393Z"}
{"level":"info","message":"AI-Enhanced Scraper Service initialized","timestamp":"2025-06-29T07:36:33.393Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T07:36:33.394Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T07:36:33.394Z"}
{"level":"info","message":"API request received to scrape URL: https://example.com","timestamp":"2025-06-29T07:39:42.640Z"}
{"level":"info","message":"Starting scrape operation for URL: https://example.com","timestamp":"2025-06-29T07:39:42.641Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-29T07:39:42.645Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-29T07:39:43.845Z"}
{"level":"info","message":"Executing task for URL: https://example.com (large website: false)","timestamp":"2025-06-29T07:39:43.846Z"}
{"level":"info","message":"Attempting scrape-https://example.com (attempt 1/4)","timestamp":"2025-06-29T07:39:43.846Z"}
{"level":"info","message":"Navigation to https://example.com successful","timestamp":"2025-06-29T07:39:45.059Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T07:39:45.060Z"}
{"level":"info","message":"Extracted metadata - Title: Example Domain, Description: N/A","timestamp":"2025-06-29T07:39:45.074Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T07:39:45.076Z"}
{"level":"info","message":"Extracted 1 links from the page","timestamp":"2025-06-29T07:39:45.081Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T07:39:45.081Z"}
{"level":"info","message":"Converted 228 characters of HTML to text","timestamp":"2025-06-29T07:39:45.105Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T07:39:45.105Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T07:39:45.107Z"}
{"level":"info","message":"Extracting enhanced about data from: https://example.com","timestamp":"2025-06-29T07:39:45.108Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://example.com","timestamp":"2025-06-29T07:39:45.109Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:39:45.110Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:39:45.111Z"}
{"globalPresence":false,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T07:39:45.114Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://example.com","timestamp":"2025-06-29T07:39:45.115Z"}
{"level":"info","message":"Finding about and team pages for https://example.com","timestamp":"2025-06-29T07:39:45.115Z"}
{"level":"info","message":"Found 0 potential about/team pages","timestamp":"2025-06-29T07:39:45.116Z"}
{"level":"info","message":"Scrape completed for https://example.com in 2500ms","timestamp":"2025-06-29T07:39:45.141Z"}
{"ip":"::1","level":"info","message":"AI Scraper API: POST /scrape","timestamp":"2025-06-29T07:39:45.221Z","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"info","message":"AI-enhanced scrape request received","timestamp":"2025-06-29T07:39:45.223Z","url":"https://example.com","userAgent":"curl/8.7.1"}
{"level":"info","message":"Starting AI-enhanced scrape for: https://example.com","timestamp":"2025-06-29T07:39:45.225Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-29T07:39:45.228Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.initCluster","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[69515:11424239:0629/130945.402482:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751182593369_hfbtmi6m9/SingletonLock: File exists (17)\n[69515:11424239:0629/130945.402673:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[69515:11424239:0629/130945.402482:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751182593369_hfbtmi6m9/SingletonLock: File exists (17)\n[69515:11424239:0629/130945.402673:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:149:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":500,"timestamp":"2025-06-29T07:39:45.413Z"}
{"code":"UNKNOWN_ERROR","error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[69515:11424239:0629/130945.402482:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751182593369_hfbtmi6m9/SingletonLock: File exists (17)\n[69515:11424239:0629/130945.402673:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","isLargeWebsite":false,"level":"error","message":"Failed to execute task for https://example.com:","retryable":false,"statusCode":500,"timestamp":"2025-06-29T07:39:45.414Z"}
{"code":"UNKNOWN_ERROR","context":"AIEnhancedScraperService.scrapeWithAI","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[69515:11424239:0629/130945.402482:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751182593369_hfbtmi6m9/SingletonLock: File exists (17)\n[69515:11424239:0629/130945.402673:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[69515:11424239:0629/130945.402482:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751182593369_hfbtmi6m9/SingletonLock: File exists (17)\n[69515:11424239:0629/130945.402673:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:149:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":500,"timestamp":"2025-06-29T07:39:45.414Z"}
{"error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[69515:11424239:0629/130945.402482:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751182593369_hfbtmi6m9/SingletonLock: File exists (17)\n[69515:11424239:0629/130945.402673:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","level":"error","message":"AI-enhanced scrape failed","stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[69515:11424239:0629/130945.402482:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751182593369_hfbtmi6m9/SingletonLock: File exists (17)\n[69515:11424239:0629/130945.402673:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:149:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","timestamp":"2025-06-29T07:39:45.415Z","url":"https://example.com"}
{"contentLength":"1116","duration":"196ms","level":"info","message":"AI Scraper API Response: POST /scrape","statusCode":500,"timestamp":"2025-06-29T07:39:45.417Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T07:52:07.999Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751183528553_wcvmp1dc8\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T07:52:08.553Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T07:52:08.604Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T07:52:08.605Z"}
{"enableAI":false,"level":"info","message":"API request received to scrape URL: https://example.com","timestamp":"2025-06-29T07:52:40.039Z"}
{"level":"info","message":"Starting scrape operation for URL: https://example.com","timestamp":"2025-06-29T07:52:40.040Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-29T07:52:40.040Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-29T07:52:40.746Z"}
{"level":"info","message":"Executing task for URL: https://example.com (large website: false)","timestamp":"2025-06-29T07:52:40.747Z"}
{"level":"info","message":"Attempting scrape-https://example.com (attempt 1/4)","timestamp":"2025-06-29T07:52:40.747Z"}
{"level":"info","message":"Navigation to https://example.com successful","timestamp":"2025-06-29T07:52:41.971Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T07:52:41.972Z"}
{"level":"info","message":"Extracted metadata - Title: Example Domain, Description: N/A","timestamp":"2025-06-29T07:52:41.982Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T07:52:41.983Z"}
{"level":"info","message":"Extracted 1 links from the page","timestamp":"2025-06-29T07:52:41.984Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T07:52:41.984Z"}
{"level":"info","message":"Converted 228 characters of HTML to text","timestamp":"2025-06-29T07:52:41.990Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T07:52:41.991Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T07:52:41.991Z"}
{"level":"info","message":"Extracting enhanced about data from: https://example.com","timestamp":"2025-06-29T07:52:41.991Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://example.com","timestamp":"2025-06-29T07:52:41.992Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:52:41.992Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:52:41.992Z"}
{"globalPresence":false,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T07:52:41.993Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://example.com","timestamp":"2025-06-29T07:52:41.993Z"}
{"level":"info","message":"Finding about and team pages for https://example.com","timestamp":"2025-06-29T07:52:41.993Z"}
{"level":"info","message":"Found 0 potential about/team pages","timestamp":"2025-06-29T07:52:41.993Z"}
{"level":"info","message":"Scrape completed for https://example.com in 1959ms","timestamp":"2025-06-29T07:52:41.999Z"}
{"enableAI":true,"level":"info","message":"API request received to scrape URL: https://example.com","timestamp":"2025-06-29T07:52:49.377Z"}
{"level":"info","message":"Starting scrape operation for URL: https://example.com","timestamp":"2025-06-29T07:52:49.377Z"}
{"level":"info","message":"Executing task for URL: https://example.com (large website: false)","timestamp":"2025-06-29T07:52:49.378Z"}
{"level":"info","message":"Attempting scrape-https://example.com (attempt 1/4)","timestamp":"2025-06-29T07:52:49.378Z"}
{"level":"info","message":"Navigation to https://example.com successful","timestamp":"2025-06-29T07:52:50.615Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T07:52:50.615Z"}
{"level":"info","message":"Extracted metadata - Title: Example Domain, Description: N/A","timestamp":"2025-06-29T07:52:50.624Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T07:52:50.624Z"}
{"level":"info","message":"Extracted 1 links from the page","timestamp":"2025-06-29T07:52:50.626Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T07:52:50.626Z"}
{"level":"info","message":"Converted 228 characters of HTML to text","timestamp":"2025-06-29T07:52:50.631Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T07:52:50.631Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T07:52:50.632Z"}
{"level":"info","message":"Extracting enhanced about data from: https://example.com","timestamp":"2025-06-29T07:52:50.632Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://example.com","timestamp":"2025-06-29T07:52:50.632Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:52:50.632Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:52:50.632Z"}
{"globalPresence":false,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T07:52:50.633Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://example.com","timestamp":"2025-06-29T07:52:50.633Z"}
{"level":"info","message":"Finding about and team pages for https://example.com","timestamp":"2025-06-29T07:52:50.633Z"}
{"level":"info","message":"Found 0 potential about/team pages","timestamp":"2025-06-29T07:52:50.633Z"}
{"level":"info","message":"Scrape completed for https://example.com in 1262ms","timestamp":"2025-06-29T07:52:50.639Z"}
{"enableAI":false,"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-29T07:54:13.534Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-29T07:54:13.535Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com (large website: true)","timestamp":"2025-06-29T07:54:13.536Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com (attempt 1/6)","timestamp":"2025-06-29T07:54:13.536Z"}
{"level":"info","message":"Navigation to https://www.semrush.com successful","timestamp":"2025-06-29T07:54:15.045Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T07:54:15.046Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-29T07:54:15.050Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T07:54:15.050Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-29T07:54:15.050Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T07:54:15.050Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-29T07:54:15.071Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T07:54:15.072Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T07:54:15.077Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.semrush.com","timestamp":"2025-06-29T07:54:15.077Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com","timestamp":"2025-06-29T07:54:15.077Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:54:15.077Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:54:15.078Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T07:54:15.080Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.semrush.com","timestamp":"2025-06-29T07:54:15.080Z"}
{"level":"info","message":"Finding about and team pages for https://www.semrush.com","timestamp":"2025-06-29T07:54:15.080Z"}
{"level":"info","message":"Found 11 potential about/team pages","timestamp":"2025-06-29T07:54:15.081Z"}
{"level":"info","message":"Found 11 about/team pages to extract","timestamp":"2025-06-29T07:54:15.081Z"}
{"level":"info","message":"Extracting enhanced about data from 11 pages","timestamp":"2025-06-29T07:54:15.081Z"}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/","timestamp":"2025-06-29T07:54:15.081Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/","timestamp":"2025-06-29T07:54:17.119Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:54:17.119Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:54:17.120Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":true,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":25,"timestamp":"2025-06-29T07:54:17.127Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/stories/","timestamp":"2025-06-29T07:54:17.128Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/stories/","timestamp":"2025-06-29T07:54:23.385Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:54:23.385Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:54:23.385Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T07:54:23.387Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.semrush.com/ in 9870ms","timestamp":"2025-06-29T07:54:23.405Z"}
{"enableAI":false,"level":"info","message":"API request received to scrape URL: https://example.com","timestamp":"2025-06-29T07:54:26.163Z"}
{"level":"info","message":"Starting scrape operation for URL: https://example.com","timestamp":"2025-06-29T07:54:26.163Z"}
{"level":"info","message":"Executing task for URL: https://example.com (large website: false)","timestamp":"2025-06-29T07:54:26.164Z"}
{"level":"info","message":"Attempting scrape-https://example.com (attempt 1/4)","timestamp":"2025-06-29T07:54:26.164Z"}
{"level":"info","message":"Navigation to https://example.com successful","timestamp":"2025-06-29T07:54:27.292Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T07:54:27.292Z"}
{"level":"info","message":"Extracted metadata - Title: Example Domain, Description: N/A","timestamp":"2025-06-29T07:54:27.295Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T07:54:27.295Z"}
{"level":"info","message":"Extracted 1 links from the page","timestamp":"2025-06-29T07:54:27.295Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T07:54:27.295Z"}
{"level":"info","message":"Converted 228 characters of HTML to text","timestamp":"2025-06-29T07:54:27.301Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T07:54:27.301Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T07:54:27.302Z"}
{"level":"info","message":"Extracting enhanced about data from: https://example.com","timestamp":"2025-06-29T07:54:27.302Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://example.com","timestamp":"2025-06-29T07:54:27.302Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:54:27.302Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:54:27.303Z"}
{"globalPresence":false,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T07:54:27.304Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://example.com","timestamp":"2025-06-29T07:54:27.304Z"}
{"level":"info","message":"Finding about and team pages for https://example.com","timestamp":"2025-06-29T07:54:27.304Z"}
{"level":"info","message":"Found 0 potential about/team pages","timestamp":"2025-06-29T07:54:27.304Z"}
{"level":"info","message":"Scrape completed for https://example.com in 1151ms","timestamp":"2025-06-29T07:54:27.314Z"}
{"enableAI":true,"level":"info","message":"API request received to scrape URL: https://example.com","timestamp":"2025-06-29T07:54:27.470Z"}
{"level":"info","message":"Starting scrape operation for URL: https://example.com","timestamp":"2025-06-29T07:54:27.471Z"}
{"level":"info","message":"Executing task for URL: https://example.com (large website: false)","timestamp":"2025-06-29T07:54:27.471Z"}
{"level":"info","message":"Attempting scrape-https://example.com (attempt 1/4)","timestamp":"2025-06-29T07:54:27.471Z"}
{"level":"info","message":"Navigation to https://example.com successful","timestamp":"2025-06-29T07:54:28.498Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T07:54:28.498Z"}
{"level":"info","message":"Extracted metadata - Title: Example Domain, Description: N/A","timestamp":"2025-06-29T07:54:28.509Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T07:54:28.509Z"}
{"level":"info","message":"Extracted 1 links from the page","timestamp":"2025-06-29T07:54:28.511Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T07:54:28.511Z"}
{"level":"info","message":"Converted 228 characters of HTML to text","timestamp":"2025-06-29T07:54:28.518Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T07:54:28.519Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T07:54:28.520Z"}
{"level":"info","message":"Extracting enhanced about data from: https://example.com","timestamp":"2025-06-29T07:54:28.520Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://example.com","timestamp":"2025-06-29T07:54:28.520Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:54:28.520Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:54:28.520Z"}
{"globalPresence":false,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T07:54:28.521Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://example.com","timestamp":"2025-06-29T07:54:28.522Z"}
{"level":"info","message":"Finding about and team pages for https://example.com","timestamp":"2025-06-29T07:54:28.522Z"}
{"level":"info","message":"Found 0 potential about/team pages","timestamp":"2025-06-29T07:54:28.522Z"}
{"level":"info","message":"Scrape completed for https://example.com in 1060ms","timestamp":"2025-06-29T07:54:28.531Z"}
{"enableAI":true,"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-29T07:54:34.143Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-29T07:54:34.143Z"}
{"level":"info","message":"Rate limiter waiting 9392ms before next request","timestamp":"2025-06-29T07:54:34.144Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com (large website: true)","timestamp":"2025-06-29T07:54:43.537Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com (attempt 1/6)","timestamp":"2025-06-29T07:54:43.537Z"}
{"level":"info","message":"Navigation to https://www.semrush.com successful","timestamp":"2025-06-29T07:54:44.859Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T07:54:44.859Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-29T07:54:44.862Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T07:54:44.862Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-29T07:54:44.864Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T07:54:44.864Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-29T07:54:44.880Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T07:54:44.880Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T07:54:44.881Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.semrush.com","timestamp":"2025-06-29T07:54:44.881Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com","timestamp":"2025-06-29T07:54:44.881Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:54:44.881Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:54:44.881Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T07:54:44.883Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.semrush.com","timestamp":"2025-06-29T07:54:44.883Z"}
{"level":"info","message":"Finding about and team pages for https://www.semrush.com","timestamp":"2025-06-29T07:54:44.883Z"}
{"level":"info","message":"Found 11 potential about/team pages","timestamp":"2025-06-29T07:54:44.883Z"}
{"level":"info","message":"Found 11 about/team pages to extract","timestamp":"2025-06-29T07:54:44.883Z"}
{"level":"info","message":"Extracting enhanced about data from 11 pages","timestamp":"2025-06-29T07:54:44.883Z"}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/","timestamp":"2025-06-29T07:54:44.883Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/","timestamp":"2025-06-29T07:54:46.917Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:54:46.917Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:54:46.918Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":true,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":25,"timestamp":"2025-06-29T07:54:46.926Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/stories/","timestamp":"2025-06-29T07:54:46.927Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/stories/","timestamp":"2025-06-29T07:54:50.158Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T07:54:50.158Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T07:54:50.159Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T07:54:50.161Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.semrush.com/ in 16037ms","timestamp":"2025-06-29T07:54:50.180Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T08:09:09.172Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751184549356_4q2lcli1m\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T08:09:09.356Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T08:09:09.382Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T08:09:09.382Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T08:09:40.657Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751184580814_ntbfla5qq\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T08:09:40.814Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T08:09:40.849Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T08:09:40.849Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T08:10:19.782Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751184619970_zwcrh2hi5\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T08:10:19.970Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T08:10:19.994Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T08:10:19.994Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T08:10:52.892Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751184653058_8qj2r5xfu\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T08:10:53.058Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T08:10:53.082Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T08:10:53.082Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T08:11:29.796Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751184689960_3hp0kgl7v\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T08:11:29.960Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T08:11:29.984Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T08:11:29.984Z"}
{"enableAI":true,"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-29T08:12:06.405Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-29T08:12:06.406Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-29T08:12:06.406Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-29T08:12:07.037Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com (large website: true)","timestamp":"2025-06-29T08:12:07.037Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com (attempt 1/6)","timestamp":"2025-06-29T08:12:07.038Z"}
{"level":"info","message":"Navigation to https://www.semrush.com successful","timestamp":"2025-06-29T08:12:08.479Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T08:12:08.479Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-29T08:12:08.481Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T08:12:08.481Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-29T08:12:08.482Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T08:12:08.482Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-29T08:12:08.510Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T08:12:08.510Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T08:12:08.511Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.semrush.com","timestamp":"2025-06-29T08:12:08.511Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com","timestamp":"2025-06-29T08:12:08.511Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T08:12:08.511Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T08:12:08.511Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T08:12:08.513Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.semrush.com","timestamp":"2025-06-29T08:12:08.513Z"}
{"level":"info","message":"Finding about and team pages for https://www.semrush.com","timestamp":"2025-06-29T08:12:08.513Z"}
{"level":"info","message":"Found 11 potential about/team pages","timestamp":"2025-06-29T08:12:08.514Z"}
{"level":"info","message":"Found 11 about/team pages to extract","timestamp":"2025-06-29T08:12:08.514Z"}
{"level":"info","message":"Extracting enhanced about data from 11 pages","timestamp":"2025-06-29T08:12:08.514Z"}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/","timestamp":"2025-06-29T08:12:08.514Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/","timestamp":"2025-06-29T08:12:11.590Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T08:12:11.590Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T08:12:11.591Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":true,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":25,"timestamp":"2025-06-29T08:12:11.601Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/stories/","timestamp":"2025-06-29T08:12:11.608Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/stories/","timestamp":"2025-06-29T08:12:35.534Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T08:12:35.534Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T08:12:35.535Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T08:12:35.539Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.semrush.com/ in 29187ms","timestamp":"2025-06-29T08:12:35.592Z"}
{"enableAI":true,"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-29T08:13:22.929Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-29T08:13:22.929Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com (large website: true)","timestamp":"2025-06-29T08:13:22.930Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com (attempt 1/6)","timestamp":"2025-06-29T08:13:22.930Z"}
{"level":"info","message":"Navigation to https://www.semrush.com successful","timestamp":"2025-06-29T08:13:24.243Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T08:13:24.243Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-29T08:13:24.247Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T08:13:24.247Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-29T08:13:24.251Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T08:13:24.251Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-29T08:13:24.269Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T08:13:24.270Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T08:13:24.270Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.semrush.com","timestamp":"2025-06-29T08:13:24.271Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com","timestamp":"2025-06-29T08:13:24.271Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T08:13:24.271Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T08:13:24.271Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T08:13:24.273Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.semrush.com","timestamp":"2025-06-29T08:13:24.273Z"}
{"level":"info","message":"Finding about and team pages for https://www.semrush.com","timestamp":"2025-06-29T08:13:24.273Z"}
{"level":"info","message":"Found 11 potential about/team pages","timestamp":"2025-06-29T08:13:24.273Z"}
{"level":"info","message":"Found 11 about/team pages to extract","timestamp":"2025-06-29T08:13:24.273Z"}
{"level":"info","message":"Extracting enhanced about data from 11 pages","timestamp":"2025-06-29T08:13:24.273Z"}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/","timestamp":"2025-06-29T08:13:24.273Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/","timestamp":"2025-06-29T08:13:26.316Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T08:13:26.316Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T08:13:26.317Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":true,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":25,"timestamp":"2025-06-29T08:13:26.325Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/stories/","timestamp":"2025-06-29T08:13:26.326Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/stories/","timestamp":"2025-06-29T08:13:29.435Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T08:13:29.436Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T08:13:29.437Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T08:13:29.440Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.semrush.com/ in 6539ms","timestamp":"2025-06-29T08:13:29.468Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T12:30:25.025Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751200225218_s4xps2qda\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T12:30:25.218Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T12:30:25.246Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T12:30:25.246Z"}
{"enableAI":true,"level":"info","message":"API request received to scrape URL: https://www.semrush.com/","timestamp":"2025-06-29T12:30:29.713Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.semrush.com/","timestamp":"2025-06-29T12:30:29.714Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-29T12:30:29.715Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-29T12:30:32.391Z"}
{"level":"info","message":"Executing task for URL: https://www.semrush.com (large website: true)","timestamp":"2025-06-29T12:30:32.395Z"}
{"level":"info","message":"Attempting large-website-scrape-https://www.semrush.com (attempt 1/6)","timestamp":"2025-06-29T12:30:32.396Z"}
{"level":"info","message":"Navigation to https://www.semrush.com successful","timestamp":"2025-06-29T12:30:34.520Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T12:30:34.521Z"}
{"level":"info","message":"Extracted metadata - Title: Semrush: Data-Driven Marketing..., Description: Drive growth in SEO, PPC, cont...","timestamp":"2025-06-29T12:30:34.537Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T12:30:34.537Z"}
{"level":"info","message":"Extracted 120 links from the page","timestamp":"2025-06-29T12:30:34.544Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T12:30:34.544Z"}
{"level":"info","message":"Converted 15966 characters of HTML to text","timestamp":"2025-06-29T12:30:34.596Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T12:30:34.597Z"}
{"level":"info","message":"Extracted 0 tables from the page","timestamp":"2025-06-29T12:30:34.598Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.semrush.com","timestamp":"2025-06-29T12:30:34.599Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com","timestamp":"2025-06-29T12:30:34.599Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T12:30:34.599Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T12:30:34.600Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T12:30:34.602Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.semrush.com","timestamp":"2025-06-29T12:30:34.603Z"}
{"level":"info","message":"Finding about and team pages for https://www.semrush.com","timestamp":"2025-06-29T12:30:34.603Z"}
{"level":"info","message":"Found 11 potential about/team pages","timestamp":"2025-06-29T12:30:34.605Z"}
{"level":"info","message":"Found 11 about/team pages to extract","timestamp":"2025-06-29T12:30:34.606Z"}
{"level":"info","message":"Extracting enhanced about data from 11 pages","timestamp":"2025-06-29T12:30:34.606Z"}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/","timestamp":"2025-06-29T12:30:34.606Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/","timestamp":"2025-06-29T12:30:37.467Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T12:30:37.468Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T12:30:37.469Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":true,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":25,"timestamp":"2025-06-29T12:30:37.483Z","valuesCount":0}
{"level":"info","message":"Visiting page: https://www.semrush.com/company/stories/","timestamp":"2025-06-29T12:30:37.487Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.semrush.com/company/stories/","timestamp":"2025-06-29T12:30:41.743Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T12:30:41.745Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T12:30:41.758Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":3,"teamMembersCount":0,"timestamp":"2025-06-29T12:30:41.778Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.semrush.com/ in 12108ms","timestamp":"2025-06-29T12:30:41.822Z"}
{"enableAI":true,"level":"info","message":"API request received to scrape URL: https://www.firecrawl.dev/","timestamp":"2025-06-29T12:32:57.763Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.firecrawl.dev/","timestamp":"2025-06-29T12:32:57.779Z"}
{"level":"info","message":"Executing task for URL: https://www.firecrawl.dev (large website: false)","timestamp":"2025-06-29T12:32:57.788Z"}
{"level":"info","message":"Attempting scrape-https://www.firecrawl.dev (attempt 1/4)","timestamp":"2025-06-29T12:32:57.789Z"}
{"level":"info","message":"Navigation to https://www.firecrawl.dev successful","timestamp":"2025-06-29T12:32:59.887Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T12:32:59.888Z"}
{"level":"info","message":"Extracted metadata - Title: Firecrawl, Description: Turn any website into LLM-read...","timestamp":"2025-06-29T12:32:59.897Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T12:32:59.897Z"}
{"level":"info","message":"Extracted 118 links from the page","timestamp":"2025-06-29T12:32:59.900Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T12:32:59.901Z"}
{"level":"info","message":"Converted 14564 characters of HTML to text","timestamp":"2025-06-29T12:32:59.923Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T12:32:59.923Z"}
{"level":"info","message":"Extracted 1 tables from the page","timestamp":"2025-06-29T12:32:59.924Z"}
{"level":"info","message":"Replacing 1 table placeholders","timestamp":"2025-06-29T12:32:59.924Z"}
{"level":"info","message":"Table placeholder replacement completed","timestamp":"2025-06-29T12:32:59.928Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.firecrawl.dev","timestamp":"2025-06-29T12:32:59.928Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.firecrawl.dev","timestamp":"2025-06-29T12:32:59.928Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T12:32:59.928Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T12:32:59.929Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T12:32:59.931Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.firecrawl.dev","timestamp":"2025-06-29T12:32:59.931Z"}
{"level":"info","message":"Finding about and team pages for https://www.firecrawl.dev","timestamp":"2025-06-29T12:32:59.931Z"}
{"level":"info","message":"Found 1 potential about/team pages","timestamp":"2025-06-29T12:32:59.932Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-29T12:32:59.932Z"}
{"level":"info","message":"Extracting enhanced about data from 1 pages","timestamp":"2025-06-29T12:32:59.932Z"}
{"level":"info","message":"Visiting page: https://www.linkedin.com/company/firecrawl","timestamp":"2025-06-29T12:32:59.932Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.linkedin.com/company/firecrawl","timestamp":"2025-06-29T12:33:04.481Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T12:33:04.485Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T12:33:04.488Z"}
{"globalPresence":false,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T12:33:04.497Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.firecrawl.dev/ in 6763ms","timestamp":"2025-06-29T12:33:04.542Z"}
{"enableAI":false,"level":"info","message":"API request received to scrape URL: https://www.firecrawl.dev/","timestamp":"2025-06-29T12:35:41.882Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.firecrawl.dev/","timestamp":"2025-06-29T12:35:41.883Z"}
{"level":"info","message":"Executing task for URL: https://www.firecrawl.dev (large website: false)","timestamp":"2025-06-29T12:35:41.884Z"}
{"level":"info","message":"Attempting scrape-https://www.firecrawl.dev (attempt 1/4)","timestamp":"2025-06-29T12:35:41.885Z"}
{"level":"info","message":"Navigation to https://www.firecrawl.dev successful","timestamp":"2025-06-29T12:35:43.800Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T12:35:43.800Z"}
{"level":"info","message":"Extracted metadata - Title: Firecrawl, Description: Turn any website into LLM-read...","timestamp":"2025-06-29T12:35:43.809Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T12:35:43.810Z"}
{"level":"info","message":"Extracted 118 links from the page","timestamp":"2025-06-29T12:35:43.818Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T12:35:43.818Z"}
{"level":"info","message":"Converted 14588 characters of HTML to text","timestamp":"2025-06-29T12:35:43.844Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T12:35:43.845Z"}
{"level":"info","message":"Extracted 1 tables from the page","timestamp":"2025-06-29T12:35:43.846Z"}
{"level":"info","message":"Replacing 1 table placeholders","timestamp":"2025-06-29T12:35:43.846Z"}
{"level":"info","message":"Table placeholder replacement completed","timestamp":"2025-06-29T12:35:43.848Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.firecrawl.dev","timestamp":"2025-06-29T12:35:43.849Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.firecrawl.dev","timestamp":"2025-06-29T12:35:43.849Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T12:35:43.849Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T12:35:43.849Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T12:35:43.852Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.firecrawl.dev","timestamp":"2025-06-29T12:35:43.852Z"}
{"level":"info","message":"Finding about and team pages for https://www.firecrawl.dev","timestamp":"2025-06-29T12:35:43.852Z"}
{"level":"info","message":"Found 1 potential about/team pages","timestamp":"2025-06-29T12:35:43.852Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-29T12:35:43.852Z"}
{"level":"info","message":"Extracting enhanced about data from 1 pages","timestamp":"2025-06-29T12:35:43.852Z"}
{"level":"info","message":"Visiting page: https://www.linkedin.com/company/firecrawl","timestamp":"2025-06-29T12:35:43.853Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.linkedin.com/company/firecrawl","timestamp":"2025-06-29T12:35:48.083Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T12:35:48.084Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T12:35:48.084Z"}
{"globalPresence":false,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T12:35:48.090Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.firecrawl.dev/ in 6232ms","timestamp":"2025-06-29T12:35:48.115Z"}
{"enableAI":false,"level":"info","message":"API request received to scrape URL: https://www.firecrawl.dev/","timestamp":"2025-06-29T12:36:22.078Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.firecrawl.dev/","timestamp":"2025-06-29T12:36:22.078Z"}
{"level":"info","message":"Executing task for URL: https://www.firecrawl.dev (large website: false)","timestamp":"2025-06-29T12:36:22.079Z"}
{"level":"info","message":"Attempting scrape-https://www.firecrawl.dev (attempt 1/4)","timestamp":"2025-06-29T12:36:22.079Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T12:36:23.972Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751200584438_rfitl3kpc\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T12:36:24.438Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T12:36:24.492Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T12:36:24.492Z"}
{"enableAI":false,"level":"info","message":"API request received to scrape URL: https://www.firecrawl.dev/","timestamp":"2025-06-29T12:36:27.085Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.firecrawl.dev/","timestamp":"2025-06-29T12:36:27.085Z"}
{"level":"info","message":"Initializing Puppeteer cluster...","timestamp":"2025-06-29T12:36:27.086Z"}
{"level":"info","message":"Cluster initialized with max concurrency: 8","timestamp":"2025-06-29T12:36:27.953Z"}
{"level":"info","message":"Executing task for URL: https://www.firecrawl.dev (large website: false)","timestamp":"2025-06-29T12:36:27.953Z"}
{"level":"info","message":"Attempting scrape-https://www.firecrawl.dev (attempt 1/4)","timestamp":"2025-06-29T12:36:27.954Z"}
{"level":"info","message":"Navigation to https://www.firecrawl.dev successful","timestamp":"2025-06-29T12:36:31.657Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T12:36:31.658Z"}
{"level":"info","message":"Extracted metadata - Title: Firecrawl, Description: Turn any website into LLM-read...","timestamp":"2025-06-29T12:36:31.661Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T12:36:31.662Z"}
{"level":"info","message":"Extracted 118 links from the page","timestamp":"2025-06-29T12:36:31.665Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T12:36:31.665Z"}
{"level":"info","message":"Converted 14588 characters of HTML to text","timestamp":"2025-06-29T12:36:31.716Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T12:36:31.717Z"}
{"level":"info","message":"Extracted 1 tables from the page","timestamp":"2025-06-29T12:36:31.719Z"}
{"level":"info","message":"Replacing 1 table placeholders","timestamp":"2025-06-29T12:36:31.719Z"}
{"level":"info","message":"Table placeholder replacement completed","timestamp":"2025-06-29T12:36:31.724Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.firecrawl.dev","timestamp":"2025-06-29T12:36:31.725Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.firecrawl.dev","timestamp":"2025-06-29T12:36:31.725Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T12:36:31.725Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T12:36:31.726Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T12:36:31.728Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.firecrawl.dev","timestamp":"2025-06-29T12:36:31.728Z"}
{"level":"info","message":"Finding about and team pages for https://www.firecrawl.dev","timestamp":"2025-06-29T12:36:31.729Z"}
{"level":"info","message":"Found 1 potential about/team pages","timestamp":"2025-06-29T12:36:31.729Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-29T12:36:31.729Z"}
{"level":"info","message":"Extracting enhanced about data from 1 pages","timestamp":"2025-06-29T12:36:31.729Z"}
{"level":"info","message":"Visiting page: https://www.linkedin.com/company/firecrawl","timestamp":"2025-06-29T12:36:31.729Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.linkedin.com/company/firecrawl","timestamp":"2025-06-29T12:36:37.955Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T12:36:37.955Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T12:36:37.956Z"}
{"globalPresence":false,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T12:36:37.958Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.firecrawl.dev/ in 10905ms","timestamp":"2025-06-29T12:36:37.990Z"}
{"enableAI":true,"level":"info","message":"API request received to scrape URL: https://www.firecrawl.dev/","timestamp":"2025-06-29T12:36:58.115Z"}
{"level":"info","message":"Starting scrape operation for URL: https://www.firecrawl.dev/","timestamp":"2025-06-29T12:36:58.118Z"}
{"level":"info","message":"Executing task for URL: https://www.firecrawl.dev (large website: false)","timestamp":"2025-06-29T12:36:58.122Z"}
{"level":"info","message":"Attempting scrape-https://www.firecrawl.dev (attempt 1/4)","timestamp":"2025-06-29T12:36:58.122Z"}
{"level":"info","message":"Navigation to https://www.firecrawl.dev successful","timestamp":"2025-06-29T12:36:59.897Z"}
{"level":"info","message":"Extracting title and description metadata","timestamp":"2025-06-29T12:36:59.897Z"}
{"level":"info","message":"Extracted metadata - Title: Firecrawl, Description: Turn any website into LLM-read...","timestamp":"2025-06-29T12:36:59.902Z"}
{"level":"info","message":"Extracting links from page","timestamp":"2025-06-29T12:36:59.902Z"}
{"level":"info","message":"Extracted 118 links from the page","timestamp":"2025-06-29T12:36:59.906Z"}
{"level":"info","message":"Converting HTML to text","timestamp":"2025-06-29T12:36:59.906Z"}
{"level":"info","message":"Converted 14564 characters of HTML to text","timestamp":"2025-06-29T12:36:59.949Z"}
{"level":"info","message":"Extracting tables from page","timestamp":"2025-06-29T12:36:59.949Z"}
{"level":"info","message":"Extracted 1 tables from the page","timestamp":"2025-06-29T12:36:59.952Z"}
{"level":"info","message":"Replacing 1 table placeholders","timestamp":"2025-06-29T12:36:59.953Z"}
{"level":"info","message":"Table placeholder replacement completed","timestamp":"2025-06-29T12:36:59.961Z"}
{"level":"info","message":"Extracting enhanced about data from: https://www.firecrawl.dev","timestamp":"2025-06-29T12:36:59.961Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.firecrawl.dev","timestamp":"2025-06-29T12:36:59.962Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T12:36:59.962Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T12:36:59.962Z"}
{"globalPresence":true,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T12:36:59.965Z","valuesCount":0}
{"level":"info","message":"Finding about and team pages from: https://www.firecrawl.dev","timestamp":"2025-06-29T12:36:59.965Z"}
{"level":"info","message":"Finding about and team pages for https://www.firecrawl.dev","timestamp":"2025-06-29T12:36:59.965Z"}
{"level":"info","message":"Found 1 potential about/team pages","timestamp":"2025-06-29T12:36:59.965Z"}
{"level":"info","message":"Found 1 about/team pages to extract","timestamp":"2025-06-29T12:36:59.965Z"}
{"level":"info","message":"Extracting enhanced about data from 1 pages","timestamp":"2025-06-29T12:36:59.965Z"}
{"level":"info","message":"Visiting page: https://www.linkedin.com/company/firecrawl","timestamp":"2025-06-29T12:36:59.966Z"}
{"level":"info","message":"Starting enhanced about data extraction for https://www.linkedin.com/company/firecrawl","timestamp":"2025-06-29T12:37:03.986Z"}
{"level":"info","message":"Extracting company about information","timestamp":"2025-06-29T12:37:03.986Z"}
{"level":"info","message":"Extracting team member information","timestamp":"2025-06-29T12:37:03.987Z"}
{"globalPresence":false,"hasDescription":false,"hasMission":false,"hasVision":false,"keyPointsCount":0,"level":"info","message":"Enhanced extraction completed:","officeLocationsCount":0,"teamMembersCount":0,"timestamp":"2025-06-29T12:37:03.992Z","valuesCount":0}
{"level":"info","message":"Scrape completed for https://www.firecrawl.dev/ in 5899ms","timestamp":"2025-06-29T12:37:04.017Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T12:54:03.598Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751201643911_kf7z2qf6e\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T12:54:03.911Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T12:54:03.940Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T12:54:03.940Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T12:54:11.876Z"}
{"level":"info","message":"Browser config loaded: \n  - Executable path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome\n  - Headless mode: true\n  - User data directory: /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751201652078_9lyty1ds6\n  - Timeout: 30000ms\n  - Max concurrency: 8","timestamp":"2025-06-29T12:54:12.078Z"}
{"level":"info","message":"Data processor service initialized","timestamp":"2025-06-29T12:54:12.110Z"}
{"level":"info","message":"Scraper service initialized","timestamp":"2025-06-29T12:54:12.110Z"}
